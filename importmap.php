<?php

/**
 * Returns the importmap for this application.
 *
 * - "path" is a path inside the asset mapper system. Use the
 *     "debug:asset-map" command to see the full list of paths.
 *
 * - "entrypoint" (JavaScript only) set to true for any module that will
 *     be used as an "entrypoint" (and passed to the importmap() Twig function).
 *
 * The "importmap:require" command can be used to add new entries to this file.
 */
return [
    'app' => [
        'path' => './assets/app.js',
        'entrypoint' => true,
    ],
    '@symfony/stimulus-bundle' => [
        'path' => './vendor/symfony/stimulus-bundle/assets/dist/loader.js',
    ],
    '@hotwired/stimulus' => [
        'version' => '3.2.2',
    ],
    '@tailwindcss/forms' => [
        'version' => '0.5.9',
    ],
    'mini-svg-data-uri' => [
        'version' => '1.4.4',
    ],
    'tailwindcss/plugin' => [
        'version' => '3.4.13',
    ],
    'tailwindcss/defaultTheme' => [
        'version' => '3.4.13',
    ],
    'tailwindcss/colors' => [
        'version' => '3.4.13',
    ],
    'picocolors' => [
        'version' => '1.1.0',
    ],
    'flowbite' => [
        'version' => '2.5.2',
    ],
    '@popperjs/core' => [
        'version' => '2.11.8',
    ],
    'flowbite-datepicker' => [
        'version' => '1.3.0',
    ],
    'flowbite/dist/flowbite.min.css' => [
        'version' => '2.5.2',
        'type' => 'css',
    ],
    '@tailwindcss/typography' => [
        'version' => '0.5.15',
    ],
    'lodash.merge' => [
        'version' => '4.6.2',
    ],
    'lodash.castarray' => [
        'version' => '4.4.0',
    ],
    'lodash.isplainobject' => [
        'version' => '4.0.6',
    ],
    'postcss-selector-parser' => [
        'version' => '6.0.10',
    ],
    'cssesc' => [
        'version' => '3.0.0',
    ],
    'util-deprecate' => [
        'version' => '1.0.2',
    ],
];
