# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    locale: bg
    locale_required: bg|en
    locales: ['bg', 'en']
    default_admin_email: <EMAIL>
    default_domain: 'brainhealth.bg'
    default_scheme: 'https'
    app_scheme: '%env(string:default:default_scheme:DEFAULT_SCHEME)%'
    router.request_context.host: '%env(default:default_domain:SYMFONY_DEFAULT_ROUTE_HOST)%'
    router.request_context.scheme: '%env(default:default_scheme:SYMFONY_DEFAULT_ROUTE_SCHEME)%'

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
        bind:
            $locale: '%locale%'
            $locales: '%locales%'
            $adminEmail: '%env(string:default:default_admin_email:ADMIN_EMAIL)%'

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    # controllers are imported separately to make sure services can be injected
    # as action arguments even if you don't extend any base controller class
    App\Controller\:
        resource: '../src/Controller/'
        tags: ['controller.service_arguments']

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones

    App\Doctrine\EntityListener\:
        resource: '../src/Doctrine/EntityListener'
        tags: [{ name: doctrine.orm.entity_listener }]

    Psr\Container\ContainerInterface: '@service_container'
