# Read the documentation: https://symfony.com/doc/current/bundles/StofDoctrineExtensionsBundle/index.html
# See the official DoctrineExtensions documentation for more details: https://github.com/doctrine-extensions/DoctrineExtensions/tree/main/doc
stof_doctrine_extensions:
    default_locale: '%locale%'
    translation_fallback: false
    # persist_default_translation: true
    orm:
        default:
            translatable: true
            timestampable: true
            blameable: true
            sluggable: true
            tree: true
            loggable: false
            sortable: false
            softdeleteable: false
            uploadable: false
            reference_integrity: false
