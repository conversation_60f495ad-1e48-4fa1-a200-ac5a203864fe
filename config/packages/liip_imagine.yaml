# Documentation on how to configure the bundle can be found at: https://symfony.com/doc/current/bundles/LiipImagineBundle/basic-usage.html
liip_imagine:
    # valid drivers options include "gd" or "gmagick" or "imagick"
    driver: "gd"

    loaders:
        default_filesystem:
            flysystem:
                filesystem_service: default.storage

    # default loader to use for all filter sets
    data_loader: default_filesystem
    twig:
        mode: lazy

    webp:
        generate: true
    default_filter_set_settings:
        format: webp

    # define your filter sets under this option
    filter_sets:
        # an example thumbnail transformation definition
        # https://symfony.com/doc/current/bundles/LiipImagineBundle/basic-usage.html#create-thumbnails
        thumbnail_sm:
            filters:
                relative_resize: { widen: 120 }
        thumbnail_lg:
            filters:
                relative_resize: { widen: 800 }
        thumbnail_xl:
            filters:
                relative_resize: { widen: 1200 }
        portrait_small:
            filters:
                thumbnail: { size: [100, 133], mode: outbound }
                strip: ~
        portrait_medium:
            filters:
                thumbnail: { size: [360, 480], mode: outbound }
                strip: ~
        lead_image:
            filters:
                upscale: { min: [1200, 675] }
                thumbnail: { size: [1200, 675], mode: outbound }
                strip: ~
        card_image_thumbnail:
            quality: 75
            filters:
                upscale: { min: [ 600, 338 ] }
                thumbnail: { size: [ 600, 338 ], position: center, mode: outbound, allow_upscale: true }
                strip: ~
