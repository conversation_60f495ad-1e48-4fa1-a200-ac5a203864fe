security:
    # https://symfony.com/doc/current/security.html#registering-the-user-hashing-passwords
    password_hashers:
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: 'auto'

    role_hierarchy:
        ROLE_CONSULTANT_ASSISTANT: ROLE_USER
        ROLE_CONSULTANT: ROLE_CONSULTANT_ASSISTANT
        ROLE_ADMIN:
            - ROLE_CONSULTANT
        ROLE_SUPER_ADMIN: ROLE_ADMIN

   # https://symfony.com/doc/current/security.html#loading-the-user-the-user-provider

    providers:
        # used to reload user from session & other features (e.g. switch_user)
        app_user_provider:
            entity:
                class: App\Entity\User
                property: email
    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
        main:
            switch_user: { role: ROLE_SUPER_ADMIN }
            lazy: true
            provider: app_user_provider
            form_login:
                # "app_login" is the name of the route created previously
                enable_csrf: true
                login_path: app_login
                check_path: app_login
                username_parameter: email
                password_parameter: password
                default_target_path: app_homepage
            user_checker: App\Security\UserChecker
            logout:
                path: app_logout
                # where to redirect after logout
                target: app_homepage

            # activate different ways to authenticate
            # https://symfony.com/doc/current/security.html#the-firewall

            # https://symfony.com/doc/current/security/impersonating_user.html
            # switch_user: true

    # Easy way to control access for large sections of your site
    # Note: Only the *first* access control that matches will be used
    access_control:
        - { path: ^/(%locale_required%)/logout, roles: PUBLIC_ACCESS }
        - { path: ^/(%locale_required%)/login, roles: PUBLIC_ACCESS }
        - { path: ^/(%locale_required%)/admin, roles: ROLE_CONSULTANT_ASSISTANT }
        # - { path: ^/profile, roles: ROLE_USER }

when@test:
    security:
        password_hashers:
            # By default, password hashers are resource intensive and take time. This is
            # important to generate secure password hashes. In tests however, secure hashes
            # are not important, waste resources and increase test times. The following
            # reduces the work factor to the lowest possible values.
            Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
                algorithm: auto
                cost: 4 # Lowest possible value for bcrypt
                time_cost: 3 # Lowest possible value for argon
                memory_cost: 10 # Lowest possible value for argon
