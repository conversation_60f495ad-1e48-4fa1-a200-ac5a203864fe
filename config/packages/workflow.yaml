framework:
    workflows:
        contact:
            metadata:
                entity: 'Contact'
            type: state_machine
            marking_store:
                type: method
                property: state
            supports:
                - App\Entity\Contact
            places:
                - START
                - WAIT_FOR_VERIFIER
                - CANCELED
                - COMPLETED
            initial_marking: START
            transitions:
                START:
                    from: START
                    to: WAIT_FOR_VERIFIER
                    metadata:
                        color_class: 'warning'
                CANCEL:
                    from: WAIT_FOR_VERIFIER
                    to: CANCELED
                    metadata:
                        color_class: 'danger'
                COMPLETE:
                    from: WAIT_FOR_VERIFIER
                    to: COMPLETED
                    metadata:
                        color_class: 'success'
