framework:
    router:
        utf8: true

        # Configure how to generate URLs in non-HTTP contexts, such as CLI commands.
        # See https://symfony.com/doc/current/routing.html#generating-urls-in-commands
        default_uri: '%env(default:default_scheme:SYMFONY_DEFAULT_ROUTE_SCHEME)%://%env(default:default_domain:SYMFONY_DEFAULT_ROUTE_HOST)%'
        #default_uri: http://localhost

when@prod:
    framework:
        router:
            strict_requirements: null
