<?php

namespace App\Entity;

use App\Doctrine\EntityListener\ContactListener;
use App\Enum\ContactStateEnum;
use App\Repository\ContactRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use libphonenumber\PhoneNumber;
use Misd\PhoneNumberBundle\Validator\Constraints\PhoneNumber as AssertPhoneNumber;
use Symfony\Component\Validator\Constraints as Assert;
use Yokai\EnumBundle\Validator\Constraints\Enum;

#[ORM\Entity(repositoryClass: ContactRepository::class)]
#[ORM\EntityListeners([ContactListener::class])]
class Contact
{
    public const STATE_START = 'START';
    public const STATE_WAIT_FOR_VERIFIER = 'WAIT_FOR_VERIFIER';
    public const STATE_CANCELED = 'CANCELED';
    public const STATE_COMPLETED = 'COMPLETED';

    public const TRANSITION_START = 'START';
    public const TRANSITION_CANCEL = 'CANCEL';
    public const TRANSITION_COMPLETE = 'COMPLETE';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'string', length: 255)]
    #[Assert\NotBlank]
    #[Assert\Length(min: 1, max: 255)]
    private ?string $name = null;

    #[ORM\Column(type: 'string', length: 255)]
    #[Assert\NotBlank]
    #[Assert\Email]
    #[Assert\Length(min: 1, max: 255)]
    private ?string $email = null;

    #[ORM\Column(type: 'phone_number', length: 255, nullable: true)]
    #[AssertPhoneNumber]
    private ?PhoneNumber $phone = null;

    #[ORM\Column(type: 'text')]
    #[Assert\NotBlank]
    #[Assert\Length(min: 1, max: 1000)]
    private string $message = '';

    #[ORM\Column(type: 'string')]
    #[Enum(enum: ContactStateEnum::class, multiple: false)]
    private string $state = self::STATE_START;

    #[ORM\OneToMany(targetEntity: WorkflowLog::class, mappedBy: 'contact', cascade: ['persist', 'remove'], orphanRemoval: true)]
    #[ORM\OrderBy(['id' => 'DESC'])]
    private Collection $workflowLogs;

    /**
     * Use to pass data to WorkflowLog if transition is apply.
     */
    protected array $workflowLogAttributes = [];

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $subject = null;

    #[ORM\Column]
    private ?bool $sendToMe = false;

    #[ORM\Column(length: 5, nullable: true)]
    private ?string $locale = null;

    /**
     * Use to pass data to WorkflowLog if transition is apply.
     */
    private ?string $workflowLogNote = null;

    public function __construct()
    {
        $this->workflowLogs = new ArrayCollection();
    }

    public function __toString()
    {
        return (string) $this->getId();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getPhone(): ?PhoneNumber
    {
        return $this->phone;
    }

    public function setPhone(?PhoneNumber $phone): self
    {
        $this->phone = $phone;

        return $this;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function setMessage(string $message): self
    {
        $this->message = $message;

        return $this;
    }

    public function getState(): string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }

    public function getWorkflowName(): string
    {
        return 'Contact';
    }

    public function getWorkflowLogAttributes(): array
    {
        return $this->workflowLogAttributes;
    }

    public function setWorkflowLogAttributes(array $workflowLogAttributes): self
    {
        $this->workflowLogAttributes = $workflowLogAttributes;

        return $this;
    }

    /**
     * @return Collection|WorkflowLog[]
     */
    public function getWorkflowLogs(): Collection|WorkflowLog
    {
        return $this->workflowLogs;
    }

    public function addWorkflowLog(WorkflowLog $workflowLog): self
    {
        if (!$this->workflowLogs->contains($workflowLog)) {
            $this->workflowLogs[] = $workflowLog;
            $workflowLog->setContact($this);
        }

        return $this;
    }

    public function removeWorkflowLog(WorkflowLog $workflowLog): self
    {
        if ($this->workflowLogs->removeElement($workflowLog)) {
            // set the owning side to null (unless already changed)
            if ($workflowLog->getContact() === $this) {
                $workflowLog->setContact(null);
            }
        }

        return $this;
    }

    public function getSubject(): ?string
    {
        return $this->subject;
    }

    public function setSubject(?string $subject): static
    {
        $this->subject = $subject;

        return $this;
    }

    public function isSendToMe(): ?bool
    {
        return $this->sendToMe;
    }

    public function setSendToMe(bool $sendToMe): static
    {
        $this->sendToMe = $sendToMe;

        return $this;
    }

    public function getLocale(): ?string
    {
        return $this->locale;
    }

    public function setLocale(?string $locale): static
    {
        $this->locale = $locale;

        return $this;
    }

    public function getWorkflowLogNote(): ?string
    {
        return $this->workflowLogNote;
    }

    public function setWorkflowLogNote(?string $workflowLogNote): self
    {
        $this->workflowLogNote = $workflowLogNote;

        return $this;
    }
}
