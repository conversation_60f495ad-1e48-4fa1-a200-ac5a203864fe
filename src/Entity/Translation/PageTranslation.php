<?php

namespace App\Entity\Translation;

use App\Entity\Page;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Translatable\Entity\MappedSuperclass\AbstractPersonalTranslation;

#[ORM\Entity]
#[ORM\Table(name: 'page_translation')]
#[ORM\UniqueConstraint(name: 'lookup_unique_idx', columns: ['locale', 'object_id', 'field'])]
class PageTranslation extends AbstractPersonalTranslation
{
    public static function init(string $locale, string $field, string $content): self
    {
        $self = new self();
        $self
            ->setLocale($locale)
            ->setField($field)
            ->setContent($content);

        return $self;
    }

    #[ORM\ManyToOne(targetEntity: Page::class, inversedBy: 'translations')]
    #[ORM\JoinColumn(name: 'object_id', referencedColumnName: 'id', onDelete: 'CASCADE')]
    protected $object;
}
