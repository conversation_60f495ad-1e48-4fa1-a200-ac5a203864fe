<?php

namespace App\Entity\Translation;

use App\Entity\Document;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Translatable\Entity\MappedSuperclass\AbstractPersonalTranslation;

#[ORM\Entity]
#[ORM\Table(name: 'document_translation')]
#[ORM\UniqueConstraint(name: 'lookup_unique_idx', columns: ['locale', 'object_id', 'field'])]
class DocumentTranslation extends AbstractPersonalTranslation
{
    public static function init(string $locale, string $field, string $content): self
    {
        $self = new self();
        $self
            ->setLocale($locale)
            ->setField($field)
            ->setContent($content);

        return $self;
    }

    #[ORM\ManyToOne(targetEntity: Document::class, inversedBy: 'translations')]
    #[ORM\JoinColumn(name: 'object_id', referencedColumnName: 'id', onDelete: 'CASCADE')]
    protected $object;
}
