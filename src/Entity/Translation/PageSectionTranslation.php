<?php

namespace App\Entity\Translation;

use App\Entity\PageSection;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Translatable\Entity\MappedSuperclass\AbstractPersonalTranslation;

#[ORM\Entity]
#[ORM\Table(name: 'page_section_translation')]
#[ORM\UniqueConstraint(name: 'lookup_unique_idx', columns: ['locale', 'object_id', 'field'])]
class PageSectionTranslation extends AbstractPersonalTranslation
{
    public static function init(string $locale, string $field, string $content): self
    {
        $self = new self();
        $self
            ->setLocale($locale)
            ->setField($field)
            ->setContent($content);

        return $self;
    }

    #[ORM\ManyToOne(targetEntity: PageSection::class, inversedBy: 'translations')]
    #[ORM\JoinColumn(name: 'object_id', referencedColumnName: 'id', onDelete: 'CASCADE')]
    protected $object;
}
