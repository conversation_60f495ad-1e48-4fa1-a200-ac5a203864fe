<?php

namespace App\Entity;

use App\Doctrine\EntityListener\DocumentListener;
use App\Enum\DocumentAccessTypeEnum;
use App\Repository\DocumentRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\Validator\Constraints as Assert;
use Yokai\EnumBundle\Validator\Constraints\Enum;

#[ORM\Entity(repositoryClass: DocumentRepository::class)]
#[ORM\EntityListeners([DocumentListener::class])]
class Document
{
    final public const PRIVATE = 'PRIVATE';
    final public const PUBLIC = 'PUBLIC';
    final public const PROTECTED = 'PROTECTED';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[Assert\NotBlank]
    #[ORM\Column(type: 'string', length: 255)]
    private string $originator;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $name = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $filePath = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $fileMimeType = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $originalFilename = null;

    #[Assert\File(maxSize: '20M', mimeTypes: ['image/png', 'image/jpeg', 'image/jpg', 'application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/zip'])]
    protected ?UploadedFile $fileUploadedFile = null;

    #[Enum(enum: DocumentAccessTypeEnum::class)]
    #[ORM\Column(type: 'string', length: 255)]
    private string $accessType = self::PRIVATE;

    #[ORM\Column(type: 'datetime')]
    private \DateTimeInterface $createdAt;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
    }

    public function __toString(): string
    {
        return (string) ($this->getName() ?? $this->getOriginalFilename());
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getOriginator(): ?string
    {
        return $this->originator;
    }

    public function setOriginator(string $originator): self
    {
        $this->originator = $originator;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getFilePath(): ?string
    {
        return $this->filePath;
    }

    public function setFilePath(?string $filePath): self
    {
        $this->filePath = $filePath;

        return $this;
    }

    public function getFileMimeType(): ?string
    {
        return $this->fileMimeType;
    }

    public function setFileMimeType(?string $fileMimeType): self
    {
        $this->fileMimeType = $fileMimeType;

        return $this;
    }

    public function getOriginalFilename(): ?string
    {
        return $this->originalFilename;
    }

    public function setOriginalFilename(?string $originalFilename): self
    {
        $this->originalFilename = $originalFilename;

        return $this;
    }

    public function getFileUploadedFile(): ?UploadedFile
    {
        return $this->fileUploadedFile;
    }

    public function setFileUploadedFile(?UploadedFile $fileUploadedFile): self
    {
        $this->fileUploadedFile = $fileUploadedFile;
        if ($fileUploadedFile) {
            $this->fileMimeType = $fileUploadedFile->getMimeType() ?? 'application/octet-stream';
            $this->originalFilename = $fileUploadedFile->getClientOriginalName();
        }

        return $this;
    }

    public function getAccessType(): ?string
    {
        return $this->accessType;
    }

    public function setAccessType(string $accessType): self
    {
        $this->accessType = $accessType;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }
}
