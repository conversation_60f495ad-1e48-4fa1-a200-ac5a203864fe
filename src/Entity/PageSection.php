<?php

namespace App\Entity;

use App\Doctrine\EntityListener\PageSectionListener;
use App\Entity\Traits\PersonalTranslatableTrait;
use App\Entity\Translation\PageSectionTranslation;
use App\Enum\PageSectionTypeEnum;
use App\Repository\PageSectionRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Gedmo\Timestampable\Traits\TimestampableEntity;
use Gedmo\Translatable\Translatable;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\Validator\Constraints as Assert;
use Yokai\EnumBundle\Validator\Constraints\Enum;

#[Gedmo\TranslationEntity(class: PageSectionTranslation::class)]
#[ORM\Entity(repositoryClass: PageSectionRepository::class)]
#[ORM\EntityListeners([PageSectionListener::class])]
class PageSection implements Translatable
{
    use TimestampableEntity;

    use PersonalTranslatableTrait;

    public const TYPE_CARD = 'CARD';
    public const TYPE_CAROUSEL = 'CAROUSEL';
    public const TYPE_CELL_IMAGE_BOTTOM = 'CELL_IMAGE_BOTTOM';
    public const TYPE_CELL_IMAGE_LEFT = 'CELL_IMAGE_LEFT';
    public const TYPE_CELL_IMAGE_RIGHT = 'CELL_IMAGE_RIGHT';
    public const TYPE_CELL_IMAGE_TOP = 'CELL_IMAGE_TOP';
    public const TYPE_CELL_TEXT = 'CELL_TEXT';
    public const TYPE_DOCUMENT_LIST = 'DOCUMENT_LIST';
    public const TYPE_FILE = 'FILE';
    public const TYPE_GALLERY = 'GALLERY';
    public const TYPE_IMAGE = 'IMAGE';
    public const TYPE_LEAD_IMAGE = 'LEAD_IMAGE';
    public const TYPE_PDF_VIEWER = 'PDF_VIEWER';
    public const TYPE_RESPONSIVE_EMBED = 'RESPONSIVE_EMBED';
    public const TYPE_VIDEO = 'VIDEO';
    public const TYPE_FAQ = 'FAQ';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\OneToMany(targetEntity: PageSectionTranslation::class, mappedBy: 'object', cascade: ['persist', 'remove'])]
    protected Collection $translations;

    #[Enum(enum: PageSectionTypeEnum::class, multiple: false)]
    #[ORM\Column(type: 'string')]
    private string $type = self::TYPE_CELL_TEXT;

    #[Gedmo\Translatable]
    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $name = null;

    #[Gedmo\Translatable]
    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $shortDescription = null;

    #[Gedmo\Translatable]
    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $description = null;

    #[ORM\Column(type: 'smallint', nullable: true)]
    private ?int $position = 100;

    #[ORM\ManyToOne(targetEntity: Page::class, inversedBy: 'sections')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Page $page = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    #[Assert\Length(min: 1, max: 255)]
    private ?string $file = null;

    #[ORM\Column(type: 'string', length: 100, nullable: true)]
    #[Assert\Length(min: 1, max: 100)]
    private ?string $fileMimeType = null;

    #[Assert\File(maxSize: '250M', mimeTypes: ['video/mp4', 'image/webp', 'image/png', 'image/jpeg', 'image/jpg', 'application/pdf', 'application/x-pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/zip'])]
    private ?UploadedFile $fileUploadedFile = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $originalFilename = null;

    #[ORM\ManyToMany(targetEntity: Document::class, cascade: ['persist', 'remove'])]
    #[Assert\Valid]
    private Collection $documents;

    #[ORM\ManyToOne(targetEntity: Page::class)]
    #[ORM\JoinColumn(onDelete: 'SET NULL')]
    private ?Page $routeToPage = null;

    #[ORM\Column(type: Types::STRING, length: 255, nullable: true)]
    private ?string $routeToUrl = null;

    #[ORM\Column(type: Types::STRING, length: 255, nullable: true)]
    private ?string $routeToPath = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $routeLabel = null;

    public function __construct()
    {
        $this->documents = new ArrayCollection();
        $this->translations = new ArrayCollection();
    }

    public function __toString()
    {
        return sprintf('%s (%s) #%d', $this->getName(), $this->getType(), $this->getPosition());
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getShortDescription(): ?string
    {
        return $this->shortDescription;
    }

    public function setShortDescription(?string $shortDescription): self
    {
        $this->shortDescription = $shortDescription;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getPosition(): ?int
    {
        return $this->position;
    }

    public function setPosition(?int $position): self
    {
        $this->position = $position;

        return $this;
    }

    public function getPage(): ?Page
    {
        return $this->page;
    }

    public function setPage(?Page $page): self
    {
        $this->page = $page;

        return $this;
    }

    public function getFile(): ?string
    {
        return $this->file;
    }

    public function setFile(?string $file): self
    {
        $this->file = $file;

        return $this;
    }

    public function getFileMimeType(): ?string
    {
        return $this->fileMimeType;
    }

    public function setFileMimeType(?string $fileMimeType): self
    {
        $this->fileMimeType = $fileMimeType;

        return $this;
    }

    public function getFileUploadedFile(): ?UploadedFile
    {
        return $this->fileUploadedFile;
    }

    public function setFileUploadedFile(?UploadedFile $fileUploadedFile): self
    {
        $this->fileUploadedFile = $fileUploadedFile;
        $this->fileMimeType = $fileUploadedFile->getMimeType() ?? 'application/octet-stream';
        $this->originalFilename = $fileUploadedFile->getClientOriginalName();
        // Ensure Doctrine event listener will be call
        $this->updatedAt = new \DateTime();

        return $this;
    }

    public function getOriginalFilename(): ?string
    {
        return $this->originalFilename;
    }

    public function setOriginalFilename(?string $originalFilename): self
    {
        $this->originalFilename = $originalFilename;

        return $this;
    }

    /**
     * @return Collection|Document[]
     */
    public function getDocuments(): Collection
    {
        return $this->documents;
    }

    public function addDocument(Document $document): self
    {
        if (!$this->documents->contains($document)) {
            $document->setOriginator(self::class);
            $this->documents[] = $document;
        }

        return $this;
    }

    public function removeDocument(Document $document): self
    {
        if ($this->documents->contains($document)) {
            $this->documents->removeElement($document);
        }

        return $this;
    }

    public function getRouteToPage(): ?Page
    {
        return $this->routeToPage;
    }

    public function setRouteToPage(?Page $routeToPage): self
    {
        $this->routeToPage = $routeToPage;

        return $this;
    }

    public function getRouteToUrl(): ?string
    {
        return $this->routeToUrl;
    }

    public function setRouteToUrl(?string $routeToUrl): self
    {
        $this->routeToUrl = $routeToUrl;

        return $this;
    }

    public function getRouteToPath(): ?string
    {
        return $this->routeToPath;
    }

    public function setRouteToPath(?string $routeToPath): self
    {
        $this->routeToPath = $routeToPath;

        return $this;
    }

    public function hasRouteOption(): bool
    {
        return null != $this->routeToPage
            || null != $this->routeToUrl
            || null != $this->routeToPath;
    }

    public function getRouteLabel(): ?string
    {
        return $this->routeLabel;
    }

    public function setRouteLabel(?string $routeLabel): static
    {
        $this->routeLabel = $routeLabel;

        return $this;
    }
}
