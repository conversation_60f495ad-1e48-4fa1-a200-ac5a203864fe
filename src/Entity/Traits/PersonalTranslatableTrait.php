<?php

namespace App\Entity\Traits;

use Doctrine\Common\Collections\Collection;
use Gedmo\Mapping\Annotation as Gedmo;
use Gedmo\Translatable\Entity\MappedSuperclass\AbstractPersonalTranslation;

trait PersonalTranslatableTrait
{
    /**
     * @Gedmo\Locale
     * Used locale to override Translation listener`s locale
     * this is not a mapped field of entity metadata, just a simple property
     */
    #[Gedmo\Locale]
    private ?string $locale = null;

    /**
     * @var Collection|AbstractPersonalTranslation[]
     */
    protected Collection $translations;

    /**
     * @return Collection|AbstractPersonalTranslation[]
     */
    public function getTranslations(): Collection
    {
        return $this->translations;
    }

    public function addTranslation(AbstractPersonalTranslation $t): self
    {
        if (!$this->translations->contains($t)) {
            $this->translations[] = $t;
            $t->setObject($this);
        }

        return $this;
    }

    public function removeTranslation(AbstractPersonalTranslation $t): self
    {
        if ($this->translations->contains($t)) {
            $this->translations->removeElement($t);
            // set the owning side to null (unless already changed)
            if ($t->getObject() === $this) {
                $t->setObject(null);
            }
        }

        return $this;
    }

    public function getLocale(): ?string
    {
        return $this->locale;
    }

    public function setLocale(?string $locale): void
    {
        $this->locale = $locale;
    }
}
