<?php

namespace App\Entity;

use App\Doctrine\EntityListener\CategoryListener;
use App\Entity\Traits\PersonalTranslatableTrait;
use App\Entity\Translation\CategoryTranslation;
use App\Enum\CategoryTagEnum;
use App\Enum\CategoryTemplateEnum;
use App\Repository\CategoryRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\Column;
use Gedmo\Mapping\Annotation as Gedmo;
use Gedmo\Timestampable\Traits\TimestampableEntity;
use Gedmo\Translatable\Translatable;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\Validator\Constraints as Assert;
use Yokai\EnumBundle\Validator\Constraints\Enum;

#[ORM\Entity(repositoryClass: CategoryRepository::class)]
#[ORM\EntityListeners([CategoryListener::class])]
#[Gedmo\TranslationEntity(class: CategoryTranslation::class)]
#[Gedmo\Tree(type: 'materializedPath')]
class Category implements Translatable
{
    use TimestampableEntity;

    use PersonalTranslatableTrait;

    public const TAG_TOP = 'TOP';
    public const TAG_FOOTER_1 = 'FOOTER_1';
    public const TAG_FOOTER_2 = 'FOOTER_2';
    public const TAG_SOCIAL = 'SOCIAL';
    public const TAG_PARTNER = 'PARTNER';
    public const TAG_HOMEPAGE = 'HOMEPAGE';

    public const TEMPLATE_DEFAULT = 'DEFAULT';
    public const TEMPLATE_NEWS = 'NEWS';
    public const TEMPLATE_WITHOUT_IMAGE = 'WITHOUT_IMAGE';
    public const TEMPLATE_WITH_ICON = 'WITH_ICON';

    public const CACHE_KEY_TREES = 'category_root_trees';

    #[Gedmo\TreePathSource]
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\OneToMany(targetEntity: CategoryTranslation::class, mappedBy: 'object', cascade: ['persist', 'remove'])]
    protected Collection $translations;

    #[Gedmo\Blameable(on: 'create')]
    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?User $createdBy = null;

    #[Gedmo\Translatable]
    #[Column(type: 'string', length: 255)]
    #[Assert\NotBlank]
    #[Assert\Length(min: 1, max: 255)]
    private ?string $name = null;

    #[Gedmo\Translatable]
    #[Column(type: 'string', length: 255, nullable: true)]
    #[Assert\Length(min: 1, max: 255)]
    private ?string $shortDescription = null;

    #[Gedmo\Translatable]
    #[Column(type: 'text', nullable: true)]
    private ?string $description = null;

    #[ORM\ManyToMany(targetEntity: Page::class, mappedBy: 'categories')]
    #[ORM\OrderBy(['position' => 'ASC', 'id' => 'DESC'])]
    private Collection $pages;

    #[Gedmo\TreePath]
    #[Column(type: 'string', length: 255, nullable: true)]
    #[Assert\Length(min: 1, max: 255)]
    private ?string $path = null;

    #[Gedmo\TreeParent]
    #[ORM\ManyToOne(targetEntity: Category::class, inversedBy: 'children')]
    #[ORM\OrderBy(['position' => 'ASC', 'id' => 'DESC'])]
    #[ORM\JoinColumn(onDelete: 'SET NULL')]
    private ?Category $parent = null;

    #[ORM\OneToMany(targetEntity: Category::class, mappedBy: 'parent')]
    #[ORM\OrderBy(['position' => 'ASC', 'id' => 'DESC'])]
    private Collection $children;

    #[Gedmo\TreeLevel]
    #[Column(type: 'integer', nullable: true)]
    private ?int $level = null;

    #[Gedmo\Slug(fields: ['name'], updatable: false)]
    #[Column(type: 'string', length: 255)]
    #[Assert\Length(min: 1, max: 255)]
    private ?string $slug = null;

    #[Column(type: 'string', length: 255)]
    #[Enum(enum: CategoryTemplateEnum::class, multiple: false)]
    private string $template = self::TEMPLATE_DEFAULT;

    #[Column(type: 'boolean')]
    #[Assert\Type(type: 'bool')]
    private bool $isActive = true;

    #[Column(type: 'string', length: 255, nullable: true)]
    #[Assert\Length(min: 1, max: 255)]
    private ?string $image = null;

    #[Column(type: 'string', length: 100, nullable: true)]
    #[Assert\Length(min: 1, max: 100)]
    private ?string $imageMimeType = null;

    #[Assert\File(maxSize: '10M', mimeTypes: ['image/png', 'image/jpeg', 'image/jpg'])]
    private ?UploadedFile $imageUploadedFile = null;

    #[Column(type: 'integer')]
    #[Assert\Type(type: 'integer')]
    private int $position = 100;

    #[ORM\ManyToOne(targetEntity: Page::class)]
    #[ORM\JoinColumn(onDelete: 'SET NULL')]
    private ?Page $routeToPage = null;

    #[Column(type: 'string', length: 255, nullable: true)]
    private ?string $routeToUrl = null;

    #[Column(type: 'string', length: 255, nullable: true)]
    private ?string $routeToPath = null;

    #[Column(type: Types::JSON)]
    #[Enum(enum: CategoryTagEnum::class, multiple: true)]
    private array $tags = [];

    #[Column(type: 'string', length: 100, nullable: true)]
    private ?string $icon = null;

    #[Column(type: Types::JSON, nullable: true)]
    private ?array $contentLocales = [];

    public function __construct()
    {
        $this->translations = new ArrayCollection();
        $this->pages = new ArrayCollection();
        $this->children = new ArrayCollection();
    }

    public function __toString()
    {
        return (string) $this->getName();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCreatedBy(): ?User
    {
        return $this->createdBy;
    }

    public function setCreatedBy(?User $createdBy): self
    {
        $this->createdBy = $createdBy;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return Collection|Page[]
     */
    public function getPages(): Collection
    {
        return $this->pages;
    }

    public function addPage(Page $page): self
    {
        if (!$this->pages->contains($page)) {
            $this->pages[] = $page;
            $page->addCategory($this);
        }

        return $this;
    }

    public function removePage(Page $page): self
    {
        if ($this->pages->contains($page)) {
            $this->pages->removeElement($page);
            $page->removeCategory($this);
        }

        return $this;
    }

    public function getPath(): ?string
    {
        return $this->path;
    }

    public function setPath(?string $path): self
    {
        $this->path = $path;

        return $this;
    }

    public function getParent(): ?self
    {
        return $this->parent;
    }

    public function setParent(?self $parent): self
    {
        $this->parent = $parent;

        return $this;
    }

    /**
     * @return Collection|self[]
     */
    public function getChildren(): Collection
    {
        // return $this->children;

        $activeChildren = new ArrayCollection();
        /** @var Category $child */
        foreach ($this->children as $child) {
            if ($child->isActive) {
                $activeChildren->add($child);
            }
        }

        return $activeChildren;
    }

    public function addChild(self $child): self
    {
        if (!$this->children->contains($child)) {
            $this->children[] = $child;
            $child->setParent($this);
        }

        return $this;
    }

    public function removeChild(self $child): self
    {
        if ($this->children->contains($child)) {
            $this->children->removeElement($child);
            // set the owning side to null (unless already changed)
            if ($child->getParent() === $this) {
                $child->setParent(null);
            }
        }

        return $this;
    }

    public function getLevel(): ?int
    {
        return $this->level;
    }

    public function setLevel(?int $level): self
    {
        $this->level = $level;

        return $this;
    }

    public function getShortDescription(): ?string
    {
        return $this->shortDescription;
    }

    public function setShortDescription(?string $shortDescription): self
    {
        $this->shortDescription = $shortDescription;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getSlug(): ?string
    {
        return $this->slug;
    }

    public function setSlug(string $slug): self
    {
        $this->slug = $slug;

        return $this;
    }

    public function getTemplate(): string
    {
        return $this->template;
    }

    public function setTemplate(string $template): self
    {
        $this->template = $template;

        return $this;
    }

    public function getIsActive(): ?bool
    {
        return $this->isActive;
    }

    public function setIsActive(bool $isActive): self
    {
        $this->isActive = $isActive;

        return $this;
    }

    public function getImage(): ?string
    {
        return $this->image;
    }

    public function setImage(?string $image): self
    {
        $this->image = $image;

        return $this;
    }

    public function getImageMimeType(): ?string
    {
        return $this->imageMimeType;
    }

    public function setImageMimeType(?string $imageMimeType): self
    {
        $this->imageMimeType = $imageMimeType;

        return $this;
    }

    public function getImageUploadedFile(): ?UploadedFile
    {
        return $this->imageUploadedFile;
    }

    public function setImageUploadedFile(?UploadedFile $imageUploadedFile): self
    {
        $this->imageUploadedFile = $imageUploadedFile;
        if ($imageUploadedFile instanceof UploadedFile) {
            $this->imageMimeType = $imageUploadedFile->getMimeType() ?? 'application/octet-stream';
            // Ensure Doctrine event listener will be call
            $this->updatedAt = new \DateTime();
        }

        return $this;
    }

    public function getPosition(): ?int
    {
        return $this->position;
    }

    public function setPosition(int $position): self
    {
        $this->position = $position;

        return $this;
    }

    public function getRouteToPage(): ?Page
    {
        return $this->routeToPage;
    }

    public function setRouteToPage(?Page $routeToPage): self
    {
        $this->routeToPage = $routeToPage;

        return $this;
    }

    public function getRouteToUrl(): ?string
    {
        return $this->routeToUrl;
    }

    public function setRouteToUrl(?string $routeToUrl): self
    {
        $this->routeToUrl = $routeToUrl;

        return $this;
    }

    public function getRouteToPath(): ?string
    {
        return $this->routeToPath;
    }

    public function setRouteToPath(?string $routeToPath): self
    {
        $this->routeToPath = $routeToPath;

        return $this;
    }

    public function getTags(): array
    {
        return $this->tags;
    }

    public function setTags(array $tags): self
    {
        $this->tags = $tags;

        return $this;
    }

    public function getIcon(): ?string
    {
        return $this->icon;
    }

    public function setIcon(?string $icon): self
    {
        $this->icon = $icon;

        return $this;
    }

    public function getContentLocales(): ?array
    {
        return $this->contentLocales;
    }

    public function setContentLocales(?array $contentLocales): self
    {
        $this->contentLocales = $contentLocales;

        return $this;
    }
}
