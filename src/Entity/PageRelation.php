<?php

namespace App\Entity;

use App\Entity\Traits\PersonalTranslatableTrait;
use App\Entity\Translation\PageRelationTranslation;
use App\Repository\PageRelationRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Gedmo\Timestampable\Traits\TimestampableEntity;
use Gedmo\Translatable\Translatable;
use Symfony\Component\Validator\Constraints as Assert;

#[Gedmo\TranslationEntity(class: PageRelationTranslation::class)]
#[ORM\Entity(repositoryClass: PageRelationRepository::class)]
class PageRelation implements Translatable
{
    use TimestampableEntity;

    use PersonalTranslatableTrait;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\OneToMany(targetEntity: PageRelationTranslation::class, mappedBy: 'object', cascade: ['persist', 'remove'])]
    protected Collection $translations;

    #[ORM\ManyToOne(targetEntity: Page::class, inversedBy: 'pageRelations')]
    #[ORM\JoinColumn(nullable: false)]
    #[Assert\NotBlank]
    private ?Page $page = null;

    #[ORM\ManyToOne(targetEntity: Page::class)]
    #[ORM\JoinColumn(nullable: false)]
    #[Assert\NotBlank]
    private ?Page $relatedPage = null;

    #[ORM\Column(type: 'integer', options: ['default' => 100])]
    #[Assert\Type(type: 'integer')]
    private int $position = 100;

    #[Gedmo\Translatable]
    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    #[Assert\Length(min: 1, max: 255)]
    private ?string $name = null;

    #[Gedmo\Translatable]
    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    #[Assert\Length(min: 1, max: 255)]
    private ?string $shortDescription = null;

    public function __construct()
    {
        $this->translations = new ArrayCollection();
    }

    public function __toString()
    {
        return (string) $this->getRelatedPage();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPage(): ?Page
    {
        return $this->page;
    }

    public function setPage(?Page $page): self
    {
        $this->page = $page;

        return $this;
    }

    public function getRelatedPage(): ?Page
    {
        return $this->relatedPage;
    }

    public function setRelatedPage(?Page $relatedPage): self
    {
        $this->relatedPage = $relatedPage;

        return $this;
    }

    public function getPosition(): ?float
    {
        return $this->position;
    }

    public function setPosition(int $position): self
    {
        $this->position = $position;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name ?: ($this->getRelatedPage() ? $this->getRelatedPage()->getName() : null);
    }

    public function setName(?string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getShortDescription(): ?string
    {
        return $this->shortDescription ?: ($this->getRelatedPage() ? $this->getRelatedPage()->getShortDescription() : null);
    }

    public function setShortDescription(?string $shortDescription): self
    {
        $this->shortDescription = $shortDescription;

        return $this;
    }
}
