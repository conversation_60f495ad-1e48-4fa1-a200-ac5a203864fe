<?php

namespace App\Entity;

use App\Doctrine\EntityListener\PageListener;
use App\Entity\Traits\PersonalTranslatableTrait;
use App\Entity\Translation\PageTranslation;
use App\Enum\PageTagEnum;
use App\Enum\PageTemplateEnum;
use App\Repository\PageRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Gedmo\Timestampable\Traits\TimestampableEntity;
use Gedmo\Translatable\Translatable;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\Validator\Constraints as Assert;
use Yokai\EnumBundle\Validator\Constraints\Enum;

#[ORM\Table(options: ['auto_increment' => 10000])]
#[ORM\Entity(repositoryClass: PageRepository::class)]
#[ORM\EntityListeners([PageListener::class])]
#[Gedmo\TranslationEntity(class: PageTranslation::class)]
class Page implements Translatable
{
    use TimestampableEntity;

    use PersonalTranslatableTrait;

    public const TAG_HOMEPAGE = 'HOMEPAGE';
    public const TAG_HOMEPAGE_NEWS = 'HOMEPAGE_NEWS';
    public const TAG_HOMEPAGE_GALLERY = 'HOMEPAGE_GALLERY';
    public const TAG_HOMEPAGE_FAQ = 'HOMEPAGE_FAQ';
    public const TAG_DECKING = 'DECKING';
    public const TAG_DECKING_ACCESSORY = 'DECKING_ACCESSORY';
    public const TAG_SIDING = 'SIDING';
    public const TAG_SIDING_ACCESSORY = 'SIDING_ACCESSORY';

    public const TEMPLATE_DEFAULT = 'DEFAULT';
    public const TEMPLATE_NEWS = 'NEWS';
    public const TEMPLATE_PRODUCT = 'PRODUCT';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\OneToMany(targetEntity: PageTranslation::class, mappedBy: 'object', cascade: ['persist', 'remove'])]
    protected Collection $translations;

    #[Gedmo\Translatable]
    #[ORM\Column(type: 'string', length: 255)]
    #[Assert\NotBlank]
    #[Assert\Length(min: 1, max: 255)]
    private ?string $name = '';

    #[Gedmo\Translatable]
    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    #[Assert\Length(min: 1, max: 255)]
    private ?string $shortDescription = null;

    #[Gedmo\Translatable]
    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $description = null;

    #[Gedmo\Blameable(on: 'create')]
    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?User $createdBy = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    #[Assert\Length(min: 1, max: 255)]
    private ?string $image = null;

    #[ORM\Column(type: 'string', length: 100, nullable: true)]
    #[Assert\Length(min: 1, max: 100)]
    private ?string $imageMimeType = null;

    #[Assert\File(maxSize: '10M', mimeTypes: ['image/png', 'image/jpeg', 'image/jpg'])]
    private ?UploadedFile $imageUploadedFile = null;

    #[ORM\Column(type: 'integer')]
    #[Assert\Type(type: 'integer')]
    private ?int $position = 100;

    #[ORM\Column(type: 'string')]
    #[Enum(enum: PageTemplateEnum::class, multiple: false)]
    private string $template = self::TEMPLATE_DEFAULT;

    #[Gedmo\Slug(fields: ['name'], updatable: false)]
    #[ORM\Column(type: 'string', length: 255)]
    #[Assert\Length(min: 1, max: 255)]
    private ?string $slug = null;

    #[ORM\Column(type: 'boolean')]
    #[Assert\Type(type: 'bool')]
    private bool $isActive = true;

    #[ORM\OneToMany(targetEntity: PageSection::class, mappedBy: 'page', cascade: ['persist', 'remove'], orphanRemoval: true)]
    #[ORM\OrderBy(['position' => 'ASC', 'id' => 'ASC'])]
    private Collection $sections;

    #[ORM\ManyToMany(targetEntity: Category::class, inversedBy: 'pages')]
    private Collection $categories;

    #[ORM\OneToMany(targetEntity: PageRelation::class, mappedBy: 'page', cascade: ['persist', 'remove'], orphanRemoval: true)]
    #[ORM\OrderBy(['position' => 'ASC'])]
    private Collection $pageRelations;

    #[ORM\Column(type: Types::JSON)]
    #[Enum(enum: PageTagEnum::class, multiple: true)]
    private array $tags = [];

    #[ORM\Column(type: 'datetime', nullable: true)]
    private ?\DateTimeInterface $activatedAt = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $keywords = null;

    #[ORM\Column(type: 'string', length: 100, nullable: true)]
    private ?string $icon = null;

    public function __construct()
    {
        $this->sections = new ArrayCollection();
        $this->categories = new ArrayCollection();
        $this->pageRelations = new ArrayCollection();
        $this->translations = new ArrayCollection();
    }

    public function __toString()
    {
        return (string) $this->getName();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function isEnabled(): bool
    {
        return $this->getIsActive() && (null === $this->getActivatedAt() || $this->getActivatedAt() <= new \DateTime());
    }

    public function getMainCategory(): ?Category
    {
        $mainCategory = $this->getCategories()->first();

        return $mainCategory ?: null;
    }

    public function hasCategoryTag(string $tag): bool
    {
        /** @var Category $category */
        foreach ($this->getCategories() as $category) {
            if (in_array($tag, $category->getTags())) {
                return true;
            }
        }

        return false;
    }

    public function hasSectionType(array $types): bool
    {
        foreach ($this->getSections() as $section) {
            if (in_array($section->getType(), $types)) {
                return true;
            }
        }

        return false;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getCreatedBy(): ?User
    {
        return $this->createdBy;
    }

    public function setCreatedBy(?User $createdBy): self
    {
        $this->createdBy = $createdBy;

        return $this;
    }

    public function getImage(): ?string
    {
        return $this->image;
    }

    public function setImage(?string $image): self
    {
        $this->image = $image;

        return $this;
    }

    public function getImageMimeType(): ?string
    {
        return $this->imageMimeType;
    }

    public function setImageMimeType(?string $imageMimeType): self
    {
        $this->imageMimeType = $imageMimeType;

        return $this;
    }

    public function getImageUploadedFile(): ?UploadedFile
    {
        return $this->imageUploadedFile;
    }

    public function setImageUploadedFile(?UploadedFile $imageUploadedFile): self
    {
        $this->imageUploadedFile = $imageUploadedFile;
        if ($imageUploadedFile instanceof UploadedFile) {
            $this->imageMimeType = $imageUploadedFile->getMimeType() ?? 'application/octet-stream';
            // Ensure Doctrine event listener will be call
            $this->updatedAt = new \DateTime();
        }

        return $this;
    }

    public function getPosition(): ?int
    {
        return $this->position;
    }

    public function setPosition(?int $position): self
    {
        $this->position = $position;

        return $this;
    }

    /**
     * @return Collection|PageSection[]
     */
    public function getSections(): Collection
    {
        return $this->sections;
    }

    public function addSection(PageSection $section): self
    {
        if (!$this->sections->contains($section)) {
            $this->sections[] = $section;
            $section->setPage($this);
        }

        return $this;
    }

    public function removeSection(PageSection $section): self
    {
        if ($this->sections->contains($section)) {
            $this->sections->removeElement($section);
            // set the owning side to null (unless already changed)
            if ($section->getPage() === $this) {
                $section->setPage(null);
            }
        }

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    /**
     * @return Collection|Category[]
     */
    public function getCategories(): Collection
    {
        return $this->categories;
    }

    public function addCategory(Category $category): self
    {
        if (!$this->categories->contains($category)) {
            $this->categories[] = $category;
        }

        return $this;
    }

    public function removeCategory(Category $category): self
    {
        if ($this->categories->contains($category)) {
            $this->categories->removeElement($category);
        }

        return $this;
    }

    public function getShortDescription(): ?string
    {
        return $this->shortDescription;
    }

    public function setShortDescription(?string $shortDescription): self
    {
        $this->shortDescription = $shortDescription;

        return $this;
    }

    public function getTemplate(): string
    {
        return $this->template;
    }

    public function setTemplate(string $template): self
    {
        $this->template = $template;

        return $this;
    }

    public function getSlug(): ?string
    {
        return $this->slug;
    }

    public function setSlug(string $slug): self
    {
        $this->slug = $slug;

        return $this;
    }

    public function getIsActive(): ?bool
    {
        return $this->isActive;
    }

    public function setIsActive(bool $isActive): self
    {
        $this->isActive = $isActive;

        return $this;
    }

    /**
     * @return Collection|PageRelation[]
     */
    public function getPageRelations(): Collection
    {
        return $this->pageRelations;
    }

    public function addPageRelation(PageRelation $pageRelation): self
    {
        if (!$this->pageRelations->contains($pageRelation)) {
            $this->pageRelations[] = $pageRelation;
            $pageRelation->setPage($this);
        }

        return $this;
    }

    public function removePageRelation(PageRelation $pageRelation): self
    {
        if ($this->pageRelations->contains($pageRelation)) {
            $this->pageRelations->removeElement($pageRelation);
            // set the owning side to null (unless already changed)
            if ($pageRelation->getPage() === $this) {
                $pageRelation->setPage(null);
            }
        }

        return $this;
    }

    public function getTags(): array
    {
        return $this->tags;
    }

    public function setTags(array $tags): self
    {
        $this->tags = $tags;

        return $this;
    }

    public function getActivatedAt(): ?\DateTimeInterface
    {
        return $this->activatedAt;
    }

    public function setActivatedAt(?\DateTimeInterface $activatedAt): self
    {
        $this->activatedAt = $activatedAt;

        return $this;
    }

    public function getKeywords(): ?string
    {
        return $this->keywords;
    }

    public function setKeywords(?string $keywords): self
    {
        $this->keywords = $keywords;

        return $this;
    }

    public function getIcon(): ?string
    {
        return $this->icon;
    }

    public function setIcon(?string $icon): self
    {
        $this->icon = $icon;

        return $this;
    }
}
