<?php

namespace App\Entity;

use App\Repository\WorkflowLogRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\PropertyAccess\PropertyAccess;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: WorkflowLogRepository::class)]
class WorkflowLog
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[Assert\NotBlank]
    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?User $createdBy = null;

    #[ORM\Column(type: 'string', length: 255)]
    private ?string $transition = null;

    #[ORM\Column(type: 'string', length: 255)]
    private ?string $froms = null;

    #[ORM\Column(type: 'string', length: 255)]
    private ?string $tos = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $note = null;

    #[ORM\Column(type: 'datetime')]
    private ?\DateTimeInterface $createdAt;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    private array $attributes = [];

    #[ORM\ManyToOne(targetEntity: Contact::class, inversedBy: 'workflowLogs')]
    private ?Contact $contact = null;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
    }

    private function getMappingByFqcn(): array
    {
        return [
            Contact::class => 'contact',
        ];
    }

    private function hasMappingByFqcn(string $fqcn): bool
    {
        return in_array($fqcn, array_keys($this->getMappingByFqcn()));
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * Attach subject to the log.
     *
     * @return bool true on success
     */
    public function attachSubject(mixed $object): bool
    {
        $fqcn = $object::class;
        if (!$this->hasMappingByFqcn($fqcn)) {
            return false;
        }

        $field = $this->getMappingByFqcn()[$fqcn];

        $accessor = PropertyAccess::createPropertyAccessor();
        $accessor->setValue($this, $field, $object);

        if (null === $this->getCreatedBy() && $accessor->isReadable($object, 'createdBy')) {
            $this->setCreatedBy($accessor->getValue($object, 'createdBy'));
        }

        if ($accessor->isReadable($object, 'workflowLogAttributes')) {
            $this->setAttributes($accessor->getValue($object, 'workflowLogAttributes'));
        }

        if ($accessor->isWritable($object, 'lastWorkflowLog')) {
            $accessor->setValue($object, 'lastWorkflowLog', $this);
        }

        return true;
    }

    public function getSubject()
    {
        $accessor = PropertyAccess::createPropertyAccessor();

        foreach ($this->getMappingByFqcn() as $property) {
            $object = $accessor->getValue($this, $property);
            if ($object) {
                return $object;
            }
        }

        return null;
    }

    public function getSubjectFqcn(): ?string
    {
        $accessor = PropertyAccess::createPropertyAccessor();

        foreach ($this->getMappingByFqcn() as $fqcn => $property) {
            $object = $accessor->getValue($this, $property);
            if ($object) {
                return $fqcn;
            }
        }

        return null;
    }

    public function getCreatedBy(): ?User
    {
        return $this->createdBy;
    }

    public function setCreatedBy(?User $createdBy): self
    {
        $this->createdBy = $createdBy;

        return $this;
    }

    public function getTransition(): ?string
    {
        return $this->transition;
    }

    public function setTransition(string $transition): self
    {
        $this->transition = $transition;

        return $this;
    }

    public function getFroms(): ?string
    {
        return $this->froms;
    }

    public function setFroms(string $froms): self
    {
        $this->froms = $froms;

        return $this;
    }

    public function getTos(): ?string
    {
        return $this->tos;
    }

    public function setTos(string $tos): self
    {
        $this->tos = $tos;

        return $this;
    }

    public function getNote(): ?string
    {
        return $this->note;
    }

    public function setNote(?string $note): self
    {
        $this->note = $note;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getAttributes(): ?array
    {
        return $this->attributes;
    }

    public function getAttributeByKey(string $key)
    {
        return $this->attributes[$key] ?? null;
    }

    public function setAttributes(array $attributes): self
    {
        $this->attributes = $attributes;

        return $this;
    }

    public function getContact(): ?Contact
    {
        return $this->contact;
    }

    public function setContact(?Contact $contact): self
    {
        $this->contact = $contact;

        return $this;
    }
}
