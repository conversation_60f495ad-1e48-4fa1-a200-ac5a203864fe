<?php

namespace App\Enum;

use App\Entity\Document;
use Symfony\Contracts\Translation\TranslatorInterface;
use Yokai\EnumBundle\TranslatedEnum;

class DocumentAccessTypeEnum extends TranslatedEnum
{
    public function __construct(TranslatorInterface $translator)
    {
        parent::__construct([
            Document::PRIVATE,
            Document::PROTECTED,
            Document::PUBLIC,
        ], $translator, 'document.accessType.%s');
    }
}
