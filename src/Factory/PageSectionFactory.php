<?php

namespace App\Factory;

use App\Entity\PageSection;
use App\Enum\PageSectionTypeEnum;
use App\Service\UploaderHelper;
use Yokai\EnumBundle\EnumRegistry;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<AppointmentNote>
 */
final class PageSectionFactory extends PersistentProxyObjectFactory
{
    public function __construct(private readonly UploaderHelper $uploaderHelper, private readonly EnumRegistry $enums)
    {
        parent::__construct();
    }

    protected function defaults(): array|callable
    {
        $file = FakerHelper::getRandomUploadedImageFile(self::faker());

        return [
            'name' => self::faker()->name(),
            'shortDescription' => self::faker()->realText(),
            'description' => self::faker()->paragraphs(
                self::faker()->numberBetween(1, 7),
                true
            ),
            'fileUploadedFile' => $file,
            'file' => $this->uploaderHelper->upload($file, null),
            'type' => self::faker()->randomElement($this->enums->get(PageSectionTypeEnum::class)->getValues()),
            'documents' => DocumentFactory::new()->many(2),
        ];
    }

    protected function initialize(): static
    {
        // see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
        return $this
            // ->afterInstantiate(function(PageSection $pageSection) {})
        ;
    }

    public static function class(): string
    {
        return PageSection::class;
    }
}
