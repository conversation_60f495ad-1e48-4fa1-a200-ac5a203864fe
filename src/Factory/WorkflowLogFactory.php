<?php

namespace App\Factory;

use App\Entity\WorkflowLog;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<WorkflowLog>
 */
final class WorkflowLogFactory extends PersistentProxyObjectFactory
{
    public function __construct()
    {
        parent::__construct();

        // TODO inject services if required (https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#factories-as-services)
    }

    protected function defaults(): array|callable
    {
        return [
            // TODO add your default values here (https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#model-factories)
            'transition' => self::faker()->text(),
            'froms' => self::faker()->text(),
            'tos' => self::faker()->text(),
            'createdAt' => null, // TODO add DATETIME ORM type manually
        ];
    }

    protected function initialize(): static
    {
        // see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
        return $this
            // ->afterInstantiate(function(WorkflowLog $workflowLog): void {})
        ;
    }

    public static function class(): string
    {
        return WorkflowLog::class;
    }
}
