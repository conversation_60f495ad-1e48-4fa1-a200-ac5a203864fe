<?php

namespace App\Factory;

use App\Entity\Page;
use App\Enum\PageTagEnum;
use App\Enum\PageTemplateEnum;
use Yokai\EnumBundle\EnumRegistry;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<AppointmentNote>
 */
final class PageFactory extends PersistentProxyObjectFactory
{
    public function __construct(private readonly EnumRegistry $enums)
    {
        parent::__construct();
    }

    public function withName(string $name): self
    {
        return $this->with([
            'name' => $name,
        ]);
    }

    public function withImage(): self
    {
        $file = FakerHelper::getRandomUploadedImageFile(self::faker());

        return $this->with([
            'imageUploadedFile' => $file,
        ]);
    }

    public function withPageSection(): self
    {
        return $this->with([
            'sections' => PageSectionFactory::new()->many(5),
        ]);
    }

    public function withPageRelation(): self
    {
        return $this->with([
            'pageRelations' => PageRelationFactory::new()->many(2),
        ]);
    }

    protected function defaults(): array|callable
    {
        return [
            'name' => self::faker()->name(),
            'shortDescription' => self::faker()->realText(),
            'description' => self::faker()->paragraphs(
                self::faker()->numberBetween(1, 7),
                true
            ),
            'createdBy' => UserFactory::random(),
            'categories' => CategoryFactory::randomRange(1, 3),
            'template' => self::faker()->randomElement($this->enums->get(PageTemplateEnum::class)->getValues()),
            'tags' => self::faker()->randomElements($this->enums->get(PageTagEnum::class)->getValues(), random_int(1, 3)),
        ];
    }

    protected function initialize(): static
    {
        // see https://github.com/zenstruck/foundry#initialization
        return $this
            // ->afterInstantiate(function(Page $page) {})
        ;
    }

    public static function class(): string
    {
        return Page::class;
    }
}
