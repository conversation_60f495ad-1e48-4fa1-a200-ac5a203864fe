<?php

namespace App\Factory;

use Faker\Generator;
use libphonenumber\NumberParseException;
use libphonenumber\PhoneNumber;
use libphonenumber\PhoneNumberUtil;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class FakerHelper
{
    public static array $pdfFiles = [
        'pdf_1.pdf',
        'pdf_2.pdf',
        'pdf_3.pdf',
    ];

    public static array $imageFiles = [
        'img_1.jpg',
        'img_2.jpg',
        'img_3.jpg',
        'img_4.jpg',
        'img_5.jpg',
    ];
    public static array $logoFiles = [
        'logo_1.jpg',
        'logo_2.jpg',
        'logo_3.jpg',
    ];

    public static function generatePhoneNumber(Generator $faker): ?PhoneNumber
    {
        $phoneUtil = PhoneNumberUtil::getInstance();
        try {
            return $phoneUtil->parse('+359887'.$faker->numberBetween(100000, 999999));
        } catch (NumberParseException) {
            return null;
        }
    }

    public static function getRandomUploadedImageFile(Generator $faker): UploadedFile
    {
        return self::getRandomUploadedFile($faker, self::$imageFiles);
    }

    public static function getRandomUploadedPdfFile(Generator $faker): UploadedFile
    {
        return self::getRandomUploadedFile($faker, self::$pdfFiles);
    }

    public static function getRandomUploadedLogoFile(Generator $faker): UploadedFile
    {
        return self::getRandomUploadedFile($faker, self::$logoFiles);
    }

    public static function getRandomUploadedFile(Generator $faker, array $files): UploadedFile
    {
        $file = $faker->randomElement($files);
        $targetPath = sys_get_temp_dir().'/'.$file;
        $fs = new Filesystem();
        $fs->copy(__DIR__.'/files/'.$file, $targetPath, true);

        return new UploadedFile($targetPath, $file, mime_content_type($targetPath));
    }
}
