<?php

namespace App\Factory;

use App\Entity\Document;
use App\Service\UploaderHelper;
use Zenst<PERSON>ck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<Document>
 */
final class DocumentFactory extends PersistentProxyObjectFactory
{
    public function __construct(private readonly UploaderHelper $uploaderHelper)
    {
        parent::__construct();

        // TODO inject services if required (https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#factories-as-services)
    }

    public function withOriginator(string $originator): self
    {
        return $this->with([
            'originator' => $originator,
        ]);
    }

    public function withPdf(): self
    {
        $file = FakerHelper::getRandomUploadedFile(self::faker(), FakerHelper::$pdfFiles);

        return $this->with([
            'fileUploadedFile' => $file,
            'filePath' => $this->uploaderHelper->upload($file, null),
        ]);
    }

    public function withConsultant(): self
    {
        $file = FakerHelper::getRandomUploadedFile(self::faker(), FakerHelper::$consultantImages);

        return $this->with([
            'fileUploadedFile' => $file,
            'filePath' => $this->uploaderHelper->upload($file, null),
        ]);
    }

    public function withAppointment(): self
    {
        $file = FakerHelper::getRandomUploadedFile(self::faker(), FakerHelper::$appointmentImages);

        return $this->with([
            'fileUploadedFile' => $file,
            'filePath' => $this->uploaderHelper->upload($file, null),
        ]);
    }

    protected function defaults(): array|callable
    {
        return [
            'name' => self::faker()->name(),
            'accessType' => Document::PRIVATE,
            'originator' => Document::class,
        ];
    }

    protected function initialize(): static
    {
        // see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
        return $this
            // ->afterInstantiate(function(Document $document): void {})
        ;
    }

    public static function class(): string
    {
        return Document::class;
    }
}
