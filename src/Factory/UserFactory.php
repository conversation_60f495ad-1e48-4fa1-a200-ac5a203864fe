<?php

namespace App\Factory;

use App\Entity\User;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<User>
 */
final class UserFactory extends PersistentProxyObjectFactory
{
    public function __construct()
    {
        parent::__construct();

        // TODO inject services if required (https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#factories-as-services)
    }

    public function adminUser(?string $email = null, ?string $password = null): self
    {
        return $this->with([
            'email' => $email ?? self::faker()->email(),
            'plainPassword' => $password ?? self::faker()->password(),
            'roles' => ['ROLE_SUPER_ADMIN'],
            'enabled' => true,
        ]);
    }

    public function withRole(array $roles): self
    {
        return $this->with([
            'roles' => $roles,
        ]);
    }

    protected function defaults(): array|callable
    {
        return [
            'name' => self::faker()->name(),
            'email' => self::faker()->email(),
            'roles' => ['ROLE_USER'],
            'plainPassword' => self::faker()->password(),
        ];
    }

    protected function initialize(): static
    {
        // see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
        return $this
            // ->afterInstantiate(function(User $user): void {})
        ;
    }

    public static function class(): string
    {
        return User::class;
    }
}
