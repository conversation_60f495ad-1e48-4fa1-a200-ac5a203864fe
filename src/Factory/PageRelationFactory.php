<?php

namespace App\Factory;

use App\Entity\PageRelation;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<AppointmentNote>
 */
final class PageRelationFactory extends PersistentProxyObjectFactory
{
    public function __construct()
    {
        parent::__construct();

        // TODO inject services if required (https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#factories-as-services)
    }

    protected function defaults(): array|callable
    {
        return [
            // TODO add your default values here (https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#model-factories)
            'name' => self::faker()->text(80),
            'shortDescription' => self::faker()->realText(),
            'relatedPage' => PageFactory::random(),
            'position' => 100,
        ];
    }

    protected function initialize(): static
    {
        // see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
        return $this
            // ->afterInstantiate(function(PageRelation $pageRelation) {})
        ;
    }

    public static function class(): string
    {
        return PageRelation::class;
    }
}
