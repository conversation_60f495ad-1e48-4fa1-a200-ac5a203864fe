<?php

namespace App\Factory;

use App\Entity\Category;
use App\Enum\CategoryTagEnum;
use App\Enum\CategoryTemplateEnum;
use Yokai\EnumBundle\EnumRegistry;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<AppointmentNote>
 */
final class CategoryFactory extends PersistentProxyObjectFactory
{
    public function __construct(private readonly EnumRegistry $enums)
    {
        parent::__construct();
    }

    public function withImage(): self
    {
        $file = FakerHelper::getRandomUploadedImageFile(self::faker());

        return $this->with([
            'imageUploadedFile' => $file,
        ]);
    }

    protected function defaults(): array|callable
    {
        return [
            // TODO add your default values here (https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#model-factories)
            'name' => self::faker()->text(20),
            'template' => self::faker()->randomElement($this->enums->get(CategoryTemplateEnum::class)->getValues()),
            'tags' => self::faker()->randomElements($this->enums->get(CategoryTagEnum::class)->getValues(), random_int(1, 3)),
            'translations' => CategoryTranslationFactory::createMany(1),
            'shortDescription' => self::faker()->text(),
            'description' => self::faker()->text(),
            'createdBy' => UserFactory::randomOrCreate(),
            'icon' => self::faker()->randomElement(['medical-icon:i-neurology', 'lucide:brain', 'material-symbols:ecg-heart-sharp', 'material-symbols:urology', 'material-symbols:nephrology']),
        ];
    }

    protected function initialize(): static
    {
        // see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
        return $this
            // ->afterInstantiate(function(Category $category) {})
        ;
    }

    public static function class(): string
    {
        return Category::class;
    }
}
