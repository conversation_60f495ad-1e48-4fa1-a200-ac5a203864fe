<?php

namespace App\Factory;

use App\Entity\Translation\CategoryTranslation;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<AppointmentNote>
 */
final class CategoryTranslationFactory extends PersistentProxyObjectFactory
{
    public function __construct()
    {
        parent::__construct();

        // TODO inject services if required (https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#factories-as-services)
    }

    protected function defaults(): array|callable
    {
        return [
            // TODO add your default values here (https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#model-factories)
            'locale' => self::faker()->randomElement(['en']),
            'field' => 'name',
            'content' => self::faker()->text(50),
        ];
    }

    protected function initialize(): static
    {
        // see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
        return $this
            // ->afterInstantiate(function(CategoryTranslation $categoryTranslation) {})
        ;
    }

    public static function class(): string
    {
        return CategoryTranslation::class;
    }
}
