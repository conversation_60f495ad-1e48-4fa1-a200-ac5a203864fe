<?php

namespace App\EventSubscriber;

use App\Entity\User;
use App\Entity\WorkflowLog;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Workflow\Event\Event;

class WorkflowListener implements EventSubscriberInterface
{
    public function __construct(protected EntityManagerInterface $entityManager, private readonly Security $security)
    {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.transition' => 'onTransition',
        ];
    }

    public function onTransition(Event $event)
    {
        $workflowLog = new WorkflowLog();

        // Check to see if we log changes for this object
        if (!$workflowLog->attachSubject($event->getSubject())) {
            return;
        }

        $user = $this->security->getUser();
        if ($user instanceof User) {
            $workflowLog->setCreatedBy($user);
        }
        $workflowLog->setTransition($event->getTransition()->getName());
        $workflowLog->setFroms(implode(',', $event->getTransition()->getFroms()));
        $workflowLog->setTos(implode(',', $event->getTransition()->getTos()));

        $this->entityManager->persist($workflowLog);
        $this->entityManager->flush();
    }
}
