<?php

namespace App\EventSubscriber;

use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Event\BeforeCrudActionEvent;
use Gedmo\Mapping\Annotation\TranslationEntity;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class TranslationEntityEasyAdminSubscriber implements EventSubscriberInterface
{
    public function __construct(private readonly EntityManagerInterface $entityManager)
    {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            BeforeCrudActionEvent::class => 'onBeforeCrudAction',
        ];
    }

    public function onBeforeCrudAction(BeforeCrudActionEvent $event)
    {
        $request = $event->getAdminContext()?->getRequest();
        if (!$request) {
            return;
        }

        $entity = $event->getAdminContext()?->getEntity()?->getInstance();
        if (!$entity) {
            return;
        }

        if (!$request->attributes->has('_locale')) {
            return;
        }

        if (!$this->hasTranslations($entity)) {
            return;
        }

        $locale = (string) $request->attributes->get('_locale');
        $entityLocale = $entity->getLocale();
        if ($entityLocale == $locale) {
            return;
        }

        $entity->setLocale($locale);
        if ($this->entityManager->contains($entity)) {
            $this->entityManager->refresh($entity);
        }
    }

    private function hasTranslations($entity): bool
    {
        $class = new \ReflectionClass($entity);
        foreach ($class->getAttributes() as $attribute) {
            if ($attribute->getName() == TranslationEntity::class) {
                return true;
            }
        }

        return false;
    }
}
