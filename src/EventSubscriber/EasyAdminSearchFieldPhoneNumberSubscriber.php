<?php

namespace App\EventSubscriber;

use EasyCorp\Bundle\EasyAdminBundle\Event\AfterEntitySearchEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

use function Symfony\Component\String\u;

class EasyAdminSearchFieldPhoneNumberSubscriber implements EventSubscriberInterface
{
    public function __construct()
    {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            AfterEntitySearchEvent::class => 'afterEntitySearchEvent',
        ];
    }

    public function afterEntitySearchEvent(AfterEntitySearchEvent $event)
    {
        $queryBuilder = $event->getQueryBuilder();
        $searchDto = $event->getSearchDto();
        $entityDto = $event->getEntityDto();

        // See addSearchClause function in EasyCorp\Bundle\EasyAdminBundle\Orm\EntityRepository
        $configuredSearchableProperties = $searchDto->getSearchableProperties();
        $searchableProperties = empty($configuredSearchableProperties) ? $entityDto->getAllPropertyNames() : $configuredSearchableProperties;
        foreach ($searchableProperties as $propertyName) {
            if ($entityDto->isAssociation($propertyName)) {
                // skip association entity
                continue;
            } else {
                $entityName = 'entity';
                $propertyDataType = $entityDto->getPropertyDataType($propertyName);
            }

            $isPhoneNumberProperty = $propertyDataType == 'phone_number';
            if ($isPhoneNumberProperty) {
                $value = u($searchDto->getQuery())->replace(' ', '')->trim()->toString();
                $queryBuilder->orWhere(sprintf('%s.%s LIKE :query_for_phone_number', $entityName, $propertyName))
                    ->setParameter('query_for_phone_number', '%'.$value.'%');
            }
        }
    }
}
