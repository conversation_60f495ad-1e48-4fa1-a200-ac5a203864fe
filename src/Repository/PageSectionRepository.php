<?php

namespace App\Repository;

use App\Entity\Document;
use App\Entity\PageSection;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method PageSection|null find($id, $lockMode = null, $lockVersion = null)
 * @method PageSection|null findOneBy(array $criteria, array $orderBy = null)
 * @method PageSection[]    findAll()
 * @method PageSection[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class PageSectionRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, PageSection::class);
    }

    public function findObjectByDocument(Document $document)
    {
        try {
            return $this->createQueryBuilder('o')
                ->andWhere(':document MEMBER OF o.documents')
                ->setParameter('document', $document)
                ->setMaxResults(1)
                ->getQuery()
                ->getOneOrNullResult();
        } catch (NonUniqueResultException) {
            return null;
        }
    }

    // /**
    //  * @return PageSection[] Returns an array of PageSection objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('d')
            ->andWhere('d.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('d.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?PageSection
    {
        return $this->createQueryBuilder('d')
            ->andWhere('d.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
