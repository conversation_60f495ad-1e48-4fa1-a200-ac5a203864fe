<?php

namespace App\Repository;

use App\Entity\Category;
use App\Entity\Page;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\Query;
use Doctrine\Persistence\ManagerRegistry;
use Gedmo\Translatable\Query\TreeWalker\TranslationWalker;
use Pagerfanta\Doctrine\ORM\QueryAdapter;
use Pagerfanta\Pagerfanta;
use Pagerfanta\PagerfantaInterface;

/**
 * @method Page|null find($id, $lockMode = null, $lockVersion = null)
 * @method Page|null findOneBy(array $criteria, array $orderBy = null)
 * @method Page[]    findAll()
 * @method Page[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class PageRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Page::class);
    }

    public function findOneById(int $id): ?Page
    {
        $query = $this->createQueryBuilder('p')
            ->addCriteria(self::createIsActiveCriteria())
            ->andWhere('p.id = :id')
            ->setParameter('id', $id)
            ->getQuery()
        ;
        $this->addTranslationWalker($query);

        try {
            return $query->getOneOrNullResult();
        } catch (NonUniqueResultException) {
            return null;
        }
    }

    public function findOneByTemplateName(string $pageTemplateName): ?Page
    {
        $query = $this->createQueryBuilder('p')
            ->addCriteria(self::createIsActiveCriteria())
            ->andWhere('p.template = :pageTemplateName')
            ->setParameter('pageTemplateName', $pageTemplateName)
            ->setMaxResults(1)
            ->getQuery()
        ;
        $this->addTranslationWalker($query);

        try {
            return $query->getOneOrNullResult();
        } catch (NonUniqueResultException) {
            return null;
        }
    }

    public function findByTag(string $tag, int $maxResults)
    {
        return $this->createQueryBuilder('p')
            ->addCriteria(self::createIsActiveCriteria())
            ->andWhere('p.tags LIKE :tag')
            ->setParameter('tag', '%'.$tag.'%')
            ->addOrderBy('p.position', 'ASC')
            ->addOrderBy('p.id', 'DESC')
            ->setMaxResults($maxResults)
            ->getQuery()
            ->getResult()
        ;
    }

    public function listByCategory(Category $category, $page = 1): PagerfantaInterface
    {
        $query = $this->createQueryBuilder('p')
            ->addCriteria(self::createIsActiveCriteria())
            ->andWhere(':category MEMBER OF p.categories')
            ->setParameter('category', $category)
            ->addOrderBy('p.position', 'ASC')
            ->addOrderBy('p.id', 'DESC')
            ->getQuery()
        ;

        return $this->createPaginator($query, $page);
    }

    public function findByCategory(Category $category, int $maxResults)
    {
        return $this->createQueryBuilder('p')
            ->addCriteria(self::createIsActiveCriteria())
            ->andWhere(':category MEMBER OF p.categories')
            ->setParameter('category', $category)
            ->addOrderBy('p.position', 'ASC')
            ->addOrderBy('p.id', 'DESC')
            ->setMaxResults($maxResults)
            ->getQuery()
            ->getResult()
        ;
    }

    public function listByTag(string $tag, $page = 1): PagerfantaInterface
    {
        $query = $this->createQueryBuilder('p')
            ->addCriteria(self::createIsActiveCriteria())
            ->andWhere('p.tags LIKE :tag')
            ->setParameter('tag', '%'.$tag.'%')
            ->addOrderBy('p.position', 'ASC')
            ->addOrderBy('p.id', 'DESC')
            ->getQuery()
        ;

        return $this->createPaginator($query, $page);
    }

    public function findBySitemap(): Query
    {
        return $this->createQueryBuilder('p')
            ->addCriteria(self::createIsActiveCriteria())
            ->orderBy('p.id', 'DESC')
            ->getQuery()
        ;
    }

    private function addTranslationWalker(Query &$query): Query
    {
        // set the translation query hint
        $query->setHint(
            Query::HINT_CUSTOM_OUTPUT_WALKER,
            TranslationWalker::class
        );

        return $query;
    }

    public function findByKeyword($keywords, $page = 1): PagerfantaInterface
    {
        $query = $this->createQueryBuilder('p')
            ->addCriteria(self::createIsActiveCriteria())
            ->leftJoin('p.translations', 'trans_title', 'WITH', "trans_title.field = 'name'")
            ->leftJoin('p.translations', 'trans_description', 'WITH', "trans_description.field = 'description'")
            ->andWhere('p.name LIKE :keywords OR p.shortDescription LIKE :keywords OR p.description LIKE :keywords OR trans_title.content LIKE :keywords OR trans_description.content LIKE :keywords')
            ->setParameter('keywords', '%'.$keywords.'%')
            ->addOrderBy('p.id', 'DESC')
            ->getQuery()
        ;

        return $this->createPaginator($query, $page);
    }

    public function findByCategoryAndDate(Category $category, string $date, int $page = 1): PagerfantaInterface
    {
        $query = $this->createQueryBuilder('p')
            ->addCriteria(self::createIsActiveCriteria())
            ->andWhere(':category MEMBER OF p.categories')
            ->setParameter('category', $category)
            ->andWhere('p.createdAt LIKE :date')
            ->setParameter('date', $date.'%')
            ->addOrderBy('p.position', 'ASC')
            ->addOrderBy('p.id', 'DESC')
            ->getQuery()
        ;

        return $this->createPaginator($query, $page);
    }

    private function createPaginator(Query $query, int $page, int $maxPerPage = 6): PagerfantaInterface
    {
        return (new Pagerfanta(new QueryAdapter($query)))
            ->setMaxPerPage($maxPerPage)
            ->setCurrentPage($page)
        ;
    }

    public static function createIsActiveCriteria(): Criteria
    {
        return Criteria::create()
            ->andWhere(
                Criteria::expr()->andX(
                    Criteria::expr()->eq('isActive', true),
                    Criteria::expr()->orX(
                        Criteria::expr()->isNull('activatedAt'),
                        Criteria::expr()->lte('activatedAt', new \DateTime())
                    )
                )
            );
    }

    public function findByCategoryTag(string $tag, int $maxResults): mixed
    {
        return $this->createQueryBuilder('p')
            ->addCriteria(self::createIsActiveCriteria())
            ->innerJoin('p.categories', 'c')
            ->andWhere('c.tags LIKE :tag')
            ->setParameter('tag', '%'.$tag.'%')
            ->addOrderBy('RAND()')
            ->setMaxResults($maxResults)
            ->getQuery()
            ->getResult()
        ;
    }

    public function findByTemplate(string $template, int $maxResults): mixed
    {
        return $this->createQueryBuilder('p')
            ->addCriteria(self::createIsActiveCriteria())
            ->andWhere('p.template = :template')
            ->setParameter('template', $template)
            ->addOrderBy('p.position', 'ASC')
            ->addOrderBy('p.id', 'DESC')
            ->setMaxResults($maxResults)
            ->getQuery()
            ->getResult()
        ;
    }
}
