<?php

namespace App\Repository;

use App\Entity\Category;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepositoryInterface;
use Doctrine\Persistence\ManagerRegistry;
use Gedmo\Tree\Entity\Repository\MaterializedPathRepository;

/**
 * @method Category|null find($id, $lockMode = null, $lockVersion = null)
 * @method Category|null findOneBy(array $criteria, array $orderBy = null)
 * @method Category[]    findAll()
 * @method Category[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CategoryRepository extends MaterializedPathRepository implements ServiceEntityRepositoryInterface
{
    public function __construct(ManagerRegistry $registry)
    {
        $entityClass = Category::class;
        $manager = $registry->getManagerForClass($entityClass);

        if (null === $manager) {
            throw new \LogicException(sprintf('Could not find the entity manager for class "%s". Check your Doctrine configuration to make sure it is configured to load this entity’s metadata.', $entityClass));
        }

        parent::__construct(
            $manager,
            $manager->getClassMetadata($entityClass)
        );
    }

    public function findByTag(string $tag, ?int $maxResults = null)
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.tags LIKE :tag')
            ->setParameter('tag', '%'.$tag.'%')
            ->andWhere('c.isActive = 1')
            ->addOrderBy('c.position', 'ASC')
            ->addOrderBy('c.id', 'ASC')
            ->setMaxResults($maxResults)
            ->getQuery()
            ->getResult()
        ;
    }

    public static function categoryTreeToArray($categoryRootTrees, &$toArray = [])
    {
        foreach ($categoryRootTrees as $cat) {
            $toArray[$cat->getId()] = $cat;
            if (count($cat->getChildren()) > 0) {
                self::categoryTreeToArray($cat->getChildren(), $toArray);
            }
        }

        return $toArray;
    }

    public function findBySitemap()
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.isActive = 1 AND ( c.routeToPage is null AND c.routeToUrl is null )')
            ->orderBy('c.id', 'DESC')
            ->getQuery()
        ;
    }

    // /**
    //  * @return Category[] Returns an array of Category objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('c.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?Category
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
