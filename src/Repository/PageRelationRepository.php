<?php

namespace App\Repository;

use App\Entity\PageRelation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method PageRelation|null find($id, $lockMode = null, $lockVersion = null)
 * @method PageRelation|null findOneBy(array $criteria, array $orderBy = null)
 * @method PageRelation[]    findAll()
 * @method PageRelation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class PageRelationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, PageRelation::class);
    }

    // /**
    //  * @return PageRelation[] Returns an array of PageRelation objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('p.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?PageRelation
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
