<?php

namespace App\Form;

use App\Entity\Contact;
use <PERSON><PERSON><PERSON>\Recaptcha3Bundle\Form\Recaptcha3Type;
use <PERSON><PERSON><PERSON>\Recaptcha3Bundle\Validator\Constraints\Recaptcha3;
use libphonenumber\PhoneNumberFormat;
use Misd\PhoneNumberBundle\Form\Type\PhoneNumberType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\IsTrue;

class ContactType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('subject', TextType::class, [
                'label' => 'contact.form.subject',
                'required' => true,
                'attr' => ['placeholder' => 'contact.placeholder.subject'],
            ])
            ->add('name', TextType::class, [
                'label' => 'contact.form.name',
                'attr' => ['placeholder' => 'contact.placeholder.name'],
            ])
            ->add('email', EmailType::class, [
                'label' => 'contact.form.email',
                'attr' => ['placeholder' => 'contact.placeholder.email'],
            ])
            ->add('phone', PhoneNumberType::class, [
                'label' => 'contact.form.phone',
                'required' => false,
                'default_region' => 'BG',
                'format' => PhoneNumberFormat::NATIONAL,
                'attr' => ['placeholder' => 'contact.placeholder.phone'],
            ])
            ->add('message', TextareaType::class, [
                'label' => 'contact.form.message',
                'attr' => ['placeholder' => 'contact.form.info'],
            ])
            ->add('sendToMe', CheckboxType::class, [
                'label' => 'contact.form.sendToMe',
                'label_html' => true,
                'required' => false,
            ])
           ->add('agreeTerms', CheckboxType::class, [
               'label' => 'contact.form.agreeTerms',
               'translation_domain' => false,
               'label_html' => true,
               'mapped' => false,
               'constraints' => [
                   new IsTrue(),
               ],
           ])
           ->add('captcha', Recaptcha3Type::class, [
               'constraints' => new Recaptcha3(),
           ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Contact::class,
            'allow_extra_fields' => true,
        ]);
    }
}
