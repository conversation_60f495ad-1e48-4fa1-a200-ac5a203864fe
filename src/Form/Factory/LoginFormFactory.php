<?php

namespace App\Form\Factory;

use App\Form\LoginFormType;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Security\Http\Authentication\AuthenticationUtils;

class LoginFormFactory
{
    public function __construct(protected FormFactoryInterface $formFactory, protected AuthenticationUtils $authenticationUtils)
    {
    }

    public function createForm(): FormInterface
    {
        $form = $this->formFactory->create(LoginFormType::class);
        $form->get('email')->setData($this->authenticationUtils->getLastUsername());

        if ($error = $this->authenticationUtils->getLastAuthenticationError()) {
            $form->addError(new FormError($error->getMessage()));
        }

        return $form;
    }
}
