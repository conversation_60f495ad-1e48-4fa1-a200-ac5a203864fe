<?php

namespace App\Controller\Admin;

use App\Entity\Category;
use App\Entity\Contact;
use App\Entity\Page;
use App\Entity\User;
use App\Repository\ConsultantRepository;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\Dashboard;
use EasyCorp\Bundle\EasyAdminBundle\Config\MenuItem;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractDashboardController;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class DashboardController extends AbstractDashboardController
{
    #[Route(path: '/admin', name: 'app_admin')]
    public function index(): Response
    {
        $adminUrlGenerator = $this->container->get(AdminUrlGenerator::class);

        return $this->redirect($adminUrlGenerator->setController(PageCrudController::class)->generateUrl());
    }

    public function configureDashboard(): Dashboard
    {
        return Dashboard::new()
            ->setTitle('ФСМЗ')
            ->setFaviconPath('images/favicon.ico')
            ->renderContentMaximized();
    }

    public function configureMenuItems(): iterable
    {
        /** @var User $user */
        $user = $this->getUser();

        yield MenuItem::linkToCrud('admin.menu.category', 'fas fa-list', Category::class)
            ->setPermission('ROLE_ADMIN');
        yield MenuItem::linkToCrud('admin.menu.page', 'fas fa-file', Page::class)
            ->setPermission('ROLE_ADMIN');
        yield MenuItem::linkToCrud('admin.menu.users', 'fas fa-users', User::class)
            ->setPermission('ROLE_ADMIN');
        yield MenuItem::linkToCrud('admin.menu.contacts', 'fas fa-envelope', Contact::class)
            ->setPermission('ROLE_ADMIN');

    }

    public function configureActions(): Actions
    {
        return parent::configureActions()
            // ->add(Crud::PAGE_INDEX, Action::DETAIL)
            // ->update(Crud::PAGE_INDEX, Action::DETAIL, fn (Action $action) => $action->setIcon('fa fa-eye'))
            ->update(Crud::PAGE_INDEX, Action::EDIT, fn (Action $action) => $action->setIcon('fa fa-edit'))
            ->remove(Crud::PAGE_INDEX, Action::DELETE)

            ->setPermission(Action::INDEX, Action::INDEX)
            ->setPermission(Action::NEW, Action::NEW)
            ->setPermission(Action::EDIT, Action::EDIT)
            ->setPermission(Action::DETAIL, Action::DETAIL)
            ->setPermission(Action::DELETE, Action::DELETE)
            ->setPermission(Action::BATCH_DELETE, Action::BATCH_DELETE)
            ->setPermission(Action::SAVE_AND_ADD_ANOTHER, Action::SAVE_AND_ADD_ANOTHER)
            ->setPermission(Action::SAVE_AND_CONTINUE, Action::SAVE_AND_CONTINUE)
            ->setPermission(Action::SAVE_AND_RETURN, Action::SAVE_AND_RETURN)
        ;
    }
}
