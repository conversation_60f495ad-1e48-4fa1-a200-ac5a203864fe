<?php

namespace App\Controller\Admin;

use App\Entity\User;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\Filters;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IntegerField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Filter\TextFilter;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;

class UserCrudController extends AbstractCrudController
{
    public function __construct(private readonly Security $security)
    {
    }

    public static function getEntityFqcn(): string
    {
        return User::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setSearchFields(['id', 'email', 'roles'])
            ->setDefaultSort(['id' => 'DESC'])
            ->setEntityLabelInSingular('admin.menu.user')
            ->setEntityLabelInPlural('admin.menu.users');
    }

    public function configureFilters(Filters $filters): Filters
    {
        $filters
            ->add(TextFilter::new('email', 'user.form.email'));

        return $filters;
    }

    public function configureFields(string $pageName): iterable
    {
        $id = IntegerField::new('id', 'user.form.id');
        $email = TextField::new('email', 'user.form.email');
        $plainPassword = TextField::new('plainPassword', 'user.form.plainPassword')
            ->setFormType(PasswordType::class);
        $expiredAt = DateTimeField::new('expiredAt', 'user.form.expiredAt');
        $enabled = BooleanField::new('enabled', 'user.form.enabled');
        $roles = ChoiceField::new('roles', 'user.form.roles')
            ->setChoices([...$this->getAllowRoles()])
            ->setTemplatePath('admin/user/list_field_roles.html.twig')
            ->allowMultipleChoices();
        $password = TextField::new('password');

        if (Crud::PAGE_INDEX === $pageName) {
            yield from [$email, $enabled, $expiredAt, $roles];
        } elseif (Crud::PAGE_DETAIL === $pageName) {
            yield from [$id, $email, $roles, $password, $enabled, $expiredAt];
        } elseif (Crud::PAGE_NEW === $pageName || Crud::PAGE_EDIT === $pageName) {
            yield from [$email, $plainPassword, $expiredAt, $enabled, $roles];
        }
    }

    public function configureActions(Actions $actions): Actions
    {
        if ($this->security->isGranted('ROLE_SUPER_ADMIN')) {
            $impersonate = Action::new('impersonate', 'Impersonate', 'fa fa-user-lock')
                ->linkToUrl(fn (User $user): string => sprintf('admin/?_switch_user=%s', $user->getEmail()));

            $actions->add(Crud::PAGE_INDEX, $impersonate);
        }

        return $actions;
    }

    private function getAllowRoles(): iterable
    {
        yield 'security.roles.ROLE_USER' => 'ROLE_USER';

        if ($this->security->isGranted('ROLE_ADMIN')) {
            yield 'security.roles.ROLE_ADMIN' => 'ROLE_ADMIN';
        }

        if ($this->security->isGranted('ROLE_SUPER_ADMIN')) {
            yield 'security.roles.ROLE_SUPER_ADMIN' => 'ROLE_SUPER_ADMIN';
        }
    }
}
