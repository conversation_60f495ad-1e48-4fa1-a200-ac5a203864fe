<?php

namespace App\Controller\Admin;

use App\EasyAdmin\Field\FileUploaderField;
use App\EasyAdmin\Field\LiipImagineField;
use App\EasyAdmin\Field\YokaiEnumField;
use App\Entity\Category;
use App\Entity\Page;
use App\Enum\PageTagEnum;
use App\Enum\PageTemplateEnum;
use App\Repository\CategoryRepository;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\Filters;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\CollectionField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\FormField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IdField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IntegerField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextareaField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Filter\EntityFilter;
use EasyCorp\Bundle\EasyAdminBundle\Filter\NumericFilter;
use EasyCorp\Bundle\EasyAdminBundle\Filter\TextFilter;
use Symfony\Component\Form\ChoiceList\Loader\CallbackChoiceLoader;

class PageCrudController extends AbstractCrudController
{
    public function __construct(private readonly CategoryRepository $categoryRepository)
    {
    }

    public static function getEntityFqcn(): string
    {
        return Page::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setSearchFields(['id', 'name'])
            ->setDefaultSort(['id' => 'DESC'])
            ->setEntityLabelInSingular('admin.menu.page')
            ->setEntityLabelInPlural('admin.menu.pages');
    }

    public function configureFilters(Filters $filters): Filters
    {
        return $filters
            ->add(NumericFilter::new('id', 'page.form.id'))
            ->add(TextFilter::new('name', 'page.form.name'))
            ->add(EntityFilter::new('categories', 'page.form.categories'))
        ;
    }

    public function configureFields(string $pageName): iterable
    {
        $id = IdField::new('id', 'page.form.id');
        $image = LiipImagineField::new('image', 'page.form.image');

        // Tab - Text
        $tabText = FormField::addTab('page.tab.text', 'fa fa-align-left');
        $name = TextField::new('name', 'page.form.name');
        $shortDescription = TextareaField::new('shortDescription', 'page.form.shortDescription');
        $description = TextareaField::new('description', 'page.form.description');

        // Tab - Attributes
        $tabAttributes = FormField::addTab('page.tab.attributes', 'fa fa-list');
        $categories = AssociationField::new('categories', 'page.form.categories')
            ->setFormTypeOption('choice_loader', new CallbackChoiceLoader(function () {
                $categoryRootTrees = $this->categoryRepository->getRootNodes('id');

                return $this->categoryRepository->categoryTreeToArray($categoryRootTrees);
            }))
            ->setFormTypeOption('choice_label', function (Category $category) {
                return sprintf('%s %s', str_repeat('—', $category->getLevel() - 1), $category->getName());
            })
            ->setTemplatePath('admin/page/list_field_categories.html.twig')
        ;
        $position = IntegerField::new('position', 'page.form.position')->setHelp('Sort ascending');
        $tags = YokaiEnumField::new('tags', 'page.form.tags')
            ->allowMultipleChoices()
            ->setEnum(PageTagEnum::class);
        $template = YokaiEnumField::new('template', 'page.form.template')
            ->setEnum(PageTemplateEnum::class);
        $icon = TextField::new('icon', 'page.form.icon')->setHelp('Use <a href="https://ux.symfony.com/icons" target="_blank">UX Icons</a>, copy icon name here.');
        $slug = TextField::new('slug', 'page.form.slug')->setFormTypeOption('disabled', true);
        $isActive = BooleanField::new('isActive', 'page.form.isActive');
        $keywords = TextField::new('keywords', 'page.form.keywords');
        $activatedAt = DateTimeField::new('activatedAt', 'page.form.activatedAt');

        // Tab - Image
        $tabImage = FormField::addTab('page.tab.image', 'fa fa-image');
        $imageUploadedFile = FileUploaderField::new('imageUploadedFile', 'page.form.image')
            ->setPathField('image')
            ->setMimeField('imageMimeType')
            ->setDownloadRoute('app_download_image_page');

        // Tab - Relations
        $tabRelations = FormField::addTab('page.tab.relations', 'fa fa-link');
        $pageRelations = AssociationField::new('pageRelations', 'page.form.pageRelations')
            ->setCrudController(PageCrudController::class)
            ->autocomplete();

        // Tab - Sections
        $tabSections = FormField::addTab('page.tab.sections', 'fa fa-link');
        $sections = CollectionField::new('sections', 'page.form.sections')
            ->setColumns('col-md-12')
            ->useEntryCrudForm()
        ;

        if (Crud::PAGE_INDEX === $pageName) {
            yield from [$id, $name, $image, $categories, $isActive];
        } elseif (Crud::PAGE_DETAIL === $pageName) {
            yield from [$id, $name, $image, $isActive];
        } elseif (Crud::PAGE_NEW === $pageName || Crud::PAGE_EDIT === $pageName) {
            yield from [
                $tabText, $name, $shortDescription, $description,
                $tabImage, $imageUploadedFile,
                $tabAttributes,
                FormField::addColumn(8), $categories, $template, $tags, $icon,
                FormField::addColumn(4), $position, $slug, $isActive, $keywords, $activatedAt,
                $tabRelations, $pageRelations,
                $tabSections, $sections,
            ];
        }
    }
}
