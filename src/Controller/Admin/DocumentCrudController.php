<?php

namespace App\Controller\Admin;

use App\EasyAdmin\Field\FileUploaderField;
use App\Entity\Document;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\IntegerField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;

class DocumentCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return Document::class;
    }

    public function configureFields(string $pageName): iterable
    {
        $id = IntegerField::new('id', 'document.form.id');
        $name = TextField::new('name', 'document.form.name');
        $fileUploadedFile = FileUploaderField::new('fileUploadedFile', 'page_section.form.file')
            ->setColumns('col-md-6 col-xxl-5')
            ->setPathField('filePath')
            ->setMimeField('fileMimeType')
            ->setDownloadRoute('app_download_page_section_document');

        if (Crud::PAGE_INDEX === $pageName || Crud::PAGE_DETAIL === $pageName) {
            yield from [$id, $name, $fileUploadedFile];
        } elseif (Crud::PAGE_NEW === $pageName || Crud::PAGE_EDIT === $pageName) {
            yield from [$name, $fileUploadedFile];
        }
    }
}
