<?php

namespace App\Controller\Admin;

use App\EasyAdmin\Field\FileUploaderField;
use App\EasyAdmin\Field\LiipImagineField;
use App\EasyAdmin\Field\YokaiEnumField;
use App\Entity\Category;
use App\Enum\CategoryTagEnum;
use App\Enum\CategoryTemplateEnum;
use App\Repository\CategoryRepository;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\Filters;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\FormField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IdField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IntegerField;
use EasyCorp\Bundle\EasyAdminBundle\Field\NumberField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextareaField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Field\UrlField;
use EasyCorp\Bundle\EasyAdminBundle\Filter\TextFilter;
use Symfony\Component\Form\ChoiceList\Loader\CallbackChoiceLoader;

class CategoryCrudController extends AbstractCrudController
{
    public function __construct(private readonly CategoryRepository $categoryRepository, private readonly array $locales)
    {
    }

    public static function getEntityFqcn(): string
    {
        return Category::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setSearchFields(['id', 'name'])
            ->setDefaultSort(['id' => 'DESC'])
            ->setEntityLabelInSingular('admin.menu.category')
            ->setEntityLabelInPlural('admin.menu.categories')
        ;
    }

    public function configureFilters(Filters $filters): Filters
    {
        return $filters
            ->add(TextFilter::new('name', 'category.form.name'))
        ;
    }

    public function configureFields(string $pageName): iterable
    {
        $image = LiipImagineField::new('fa fa-image', 'category.form.image');
        $level = NumberField::new('level', 'category.form.level')->setFormTypeOption('disabled', true);

        // Tab - Text
        $tabText = FormField::addTab('category.tab.text', 'fa fa-align-left');
        $name = TextField::new('name', 'category.form.name');
        $shortDescription = TextareaField::new('shortDescription', 'category.form.shortDescription');
        $description = TextareaField::new('description', 'category.form.description');

        // Tab - Attributes
        $tabAttributes = FormField::addTab('category.tab.attributes', 'fa fa-list');
        $parent = ChoiceField::new('parent', 'category.form.parent')
            ->setFormTypeOption('choice_loader', new CallbackChoiceLoader(function () {
                $categoryRootTrees = $this->categoryRepository->getRootNodes('id');

                return $this->categoryRepository->categoryTreeToArray($categoryRootTrees);
            }))
            ->setFormTypeOption('choice_label', function (Category $category) {
                return sprintf('%s %s', str_repeat('—', $category->getLevel() - 1), $category->getName());
            })
            ->setHelp('category.help.parent')
        ;
        $id = IdField::new('id', 'category.form.id');
        $position = IntegerField::new('position', 'category.form.position')->setHelp('Sort ascending');
        $tags = YokaiEnumField::new('tags', 'category.form.tags')
            ->allowMultipleChoices()
            ->setEnum(CategoryTagEnum::class);
        $template = YokaiEnumField::new('template', 'category.form.template')
            ->setEnum(CategoryTemplateEnum::class);
        $icon = TextField::new('icon', 'category.form.icon')->setHelp('Use <a href="https://ux.symfony.com/icons" target="_blank">UX Icons</a>, copy icon name here.');
        $slug = TextField::new('slug', 'category.form.slug')->setFormTypeOption('disabled', true);
        $isActive = BooleanField::new('isActive', 'category.form.isActive');
        $contentLocales = ChoiceField::new('contentLocales', 'category.form.contentLocales')
            ->setChoices(function () {
                $choices = [];
                foreach ($this->locales as $locale) {
                    $choices[$locale] = $locale;
                }

                return $choices;
            })
            ->allowMultipleChoices();

        // Tab - Image
        $tabImage = FormField::addTab('category.tab.image', 'fa fa-image');
        $imageUploadedFile = FileUploaderField::new('imageUploadedFile', 'category.form.image')
            ->setPathField('image')
            ->setMimeField('imageMimeType')
            ->setDownloadRoute('app_download_image_category');

        // Tab - Links
        $tabLinks = FormField::addTab('category.tab.link', 'fa fa-link');
        $routeToUrl = UrlField::new('routeToUrl', 'category.form.routeToUrl')->setHelp('Example: https://google.com');
        $routeToPath = TextField::new('routeToPath', 'category.form.routeToPath')->setHelp('Example: page_list;{"slug":"members","id":"12"}');
        $routeToPage = AssociationField::new('routeToPage', 'category.form.routeToPage')->autocomplete();

        if (Crud::PAGE_INDEX === $pageName) {
            yield from [$id, $name, $image, $level, $isActive];
        } elseif (Crud::PAGE_DETAIL === $pageName) {
            yield from [$id, $name, $image, $level, $isActive];
        } elseif (Crud::PAGE_NEW === $pageName || Crud::PAGE_EDIT === $pageName) {
            yield from [
                $tabText, $name, $shortDescription, $description,
                $tabImage, $imageUploadedFile,
                $tabAttributes, FormField::addColumn(8), $parent, $template, $tags, $icon, FormField::addColumn(4), $position, $contentLocales, $slug, $isActive,
                $tabLinks, $routeToUrl, $routeToPath, $routeToPage,
            ];
        }
    }
}
