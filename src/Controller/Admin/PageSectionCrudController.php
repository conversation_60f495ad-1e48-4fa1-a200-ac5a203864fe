<?php

namespace App\Controller\Admin;

use App\EasyAdmin\Field\FileUploaderField;
use App\EasyAdmin\Field\YokaiEnumField;
use App\Entity\PageSection;
use App\Enum\PageSectionTypeEnum;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\CollectionField;
use EasyCorp\Bundle\EasyAdminBundle\Field\FormField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IdField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IntegerField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextareaField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Field\UrlField;

class PageSectionCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return PageSection::class;
    }

    public function configureFields(string $pageName): iterable
    {
        $id = IdField::new('id', 'page_section.form.id');

        // Tab - Text
        $tabType = FormField::addFieldset('page_section.tab.type', 'pen-to-square');
        // $page = AssociationField::new('page', 'page_section.form.page')->autocomplete();
        $type = YokaiEnumField::new('type', 'page_section.form.type')
            ->setEnum(PageSectionTypeEnum::class)
            ->setColumns('col-sm-12 col-md-8');
        $position = IntegerField::new('position', 'page.form.position')->setColumns('col-sm-12 col-md-4');

        // Tab - Text
        $tabText = FormField::addFieldset('page_section.tab.text', 'align-left');
        $name = TextField::new('name', 'page_section.form.name');
        $shortDescription = TextareaField::new('shortDescription', 'page_section.form.shortDescription')
            ->setNumOfRows(2);
        $description = TextareaField::new('description', 'page_section.form.description')
            ->setNumOfRows(3);

        // Tab - Image
        $tabFile = FormField::addFieldset('page_section.tab.image', 'image');
        $fileUploadedFile = FileUploaderField::new('fileUploadedFile', 'page_section.form.file')
            ->setPathField('file')
            ->setMimeField('fileMimeType')
            ->setDownloadRoute('app_download_page_section_file');

        // Tab - Links
        $tabLinks = FormField::addFieldset('page_section.tab.link', 'link');
        $routeToUrl = UrlField::new('routeToUrl', 'page_section.form.routeToUrl')
            ->setColumns('col-md-12')
            ->setHelp('https://google.com');
        $routeToPath = TextField::new('routeToPath', 'page_section.form.routeToPath')
            ->setColumns('col-md-12')
            ->setHelp('page_list;{"slug":"members","id":"12"}');
        $routeToPage = AssociationField::new('routeToPage', 'page_section.form.routeToPage')
            ->setColumns('col-md-12')
            ->autocomplete();

        // Tab - Sections
        $tabDocuments = FormField::addFieldset('page_section.tab.documents', 'copy');
        $documents = CollectionField::new('documents', false)
            ->setColumns('col-md-12')
            ->useEntryCrudForm();
        $routeLabel = TextField::new('routeLabel', 'page_section.form.routeLabel');

        if (Crud::PAGE_INDEX === $pageName) {
            yield from [$id, $type, $name];
        } elseif (Crud::PAGE_DETAIL === $pageName) {
            yield from [$id, $type, $name];
        } elseif (Crud::PAGE_NEW === $pageName || Crud::PAGE_EDIT === $pageName) {
            yield from [
                FormField::addColumn(12),
                $tabType, $type, $position,
                FormField::addColumn(8),
                $tabText, $name, $shortDescription, $description,
                $tabDocuments, $documents,
                FormField::addColumn(4),
                $tabFile, $fileUploadedFile,
                $tabLinks, $routeToUrl, $routeToPath, $routeToPage, $routeLabel,
            ];
        }
    }
}
