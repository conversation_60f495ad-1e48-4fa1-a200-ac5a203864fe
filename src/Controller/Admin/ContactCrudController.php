<?php

namespace App\Controller\Admin;

use App\EasyAdmin\Field\PhoneNumberField;
use App\EasyAdmin\Field\YokaiEnumField;
use App\EasyAdmin\Filter\PhoneNumberFilter;
use App\Entity\Contact;
use App\Enum\ContactStateEnum;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\Filters;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\EmailField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IdField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextareaField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Filter\NumericFilter;
use EasyCorp\Bundle\EasyAdminBundle\Filter\TextFilter;

class ContactCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return Contact::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setSearchFields(['id', 'name'])
            ->setDefaultSort(['id' => 'DESC'])
            ->setEntityLabelInSingular('admin.menu.contact')
            ->setEntityLabelInPlural('admin.menu.contacts')
        ;
    }

    public function configureFilters(Filters $filters): Filters
    {
        return $filters
            ->add(NumericFilter::new('id', 'page.form.id'))
            ->add(TextFilter::new('name', 'contact.form.name'))
            ->add(TextFilter::new('email', 'contact.form.email'))
            ->add(PhoneNumberFilter::new('phone', 'contact.form.phone'))
        ;
    }

    public function configureFields(string $pageName): iterable
    {
        $id = IdField::new('id', 'contact.form.id');
        $name = TextField::new('name', 'contact.form.name');
        $email = EmailField::new('email', 'contact.form.email');
        $phone = PhoneNumberField::new('phone', 'contact.form.phone')
            ->setFormTypeOption('default_region', 'BG');
        $message = TextareaField::new('message', 'contact.form.message');
        $state = YokaiEnumField::new('state', 'contact.form.state')
            ->setTemplatePath('admin/contact/list_field_state.html.twig')
            ->setEnum(ContactStateEnum::class);

        if (Crud::PAGE_INDEX === $pageName) {
            yield from [$id, $name, $email, $phone, $state];
        } elseif (Crud::PAGE_DETAIL === $pageName) {
            yield from [$id, $name, $email, $phone, $message, $state];
        } elseif (Crud::PAGE_NEW === $pageName || Crud::PAGE_EDIT === $pageName) {
            yield from [
                $name, $email, $phone, $message, $state,
            ];
        }
    }
}
