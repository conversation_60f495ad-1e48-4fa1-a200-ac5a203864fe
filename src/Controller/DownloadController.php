<?php

namespace App\Controller;

use App\Entity\Category;
use App\Entity\Document;
use App\Entity\Page;
use App\Entity\PageSection;
use App\Repository\PageSectionRepository;
use App\Service\UploaderHelper;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\HeaderUtils;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\String\UnicodeString;

class DownloadController extends AbstractController
{
    #[Route(path: '/download/image/category/{id}', name: 'app_download_image_category', requirements: ['id' => '\d+'])]
    public function downloadImageCategory(Category $category, UploaderHelper $uploaderHelper): StreamedResponse
    {
        return $this->downloadFile(
            $uploaderHelper,
            $category->getImage(),
            $category->getImageMimeType(),
            basename($category->getImage())
        );
    }

    #[Route(path: '/download/image/page/{id}', name: 'app_download_image_page', requirements: ['id' => '\d+'])]
    public function downloadImagePage(Page $page, UploaderHelper $uploaderHelper): StreamedResponse
    {
        return $this->downloadFile(
            $uploaderHelper,
            $page->getImage(),
            $page->getImageMimeType(),
            basename($page->getImage())
        );
    }

    #[Route(path: '/download/page-section/document/{id}', name: 'app_download_page_section_document', requirements: ['id' => '\d+'])]
    public function downloadPageSectionDocument(Document $document, UploaderHelper $uploaderHelper, PageSectionRepository $pageSectionRepository): StreamedResponse
    {
        $pageSection = $pageSectionRepository->findObjectByDocument($document);
        if (!$pageSection) {
            throw $this->createNotFoundException();
        }

        return $this->downloadFile(
            $uploaderHelper,
            $document->getFilePath(),
            $document->getFileMimeType(),
            $document->getOriginalFilename() ?? basename($document->getFilePath())
        );
    }

    #[Route(path: '/download/page-section/file/{id}', name: 'app_download_page_section_file', requirements: ['id' => '\d+'])]
    public function downloadFilePageSection(PageSection $pageSection, UploaderHelper $uploaderHelper): StreamedResponse
    {
        return $this->downloadFile(
            $uploaderHelper,
            $pageSection->getFile(),
            $pageSection->getFileMimeType(),
            $pageSection->getOriginalFilename() ?? basename($pageSection->getFile())
        );
    }

    private function downloadFile(UploaderHelper $uploaderHelper, string $filePath, string $fileMimeType, string $fileName): StreamedResponse
    {
        $response = new StreamedResponse(function () use ($uploaderHelper, $filePath) {
            $outputStream = fopen('php://output', 'wb');
            $fileStream = $uploaderHelper->readStream($filePath);
            stream_copy_to_stream($fileStream, $outputStream);
        });
        $response->headers->set('Content-Type', $fileMimeType);
        $disposition = HeaderUtils::makeDisposition(
            HeaderUtils::DISPOSITION_ATTACHMENT,
            (new UnicodeString($fileName))->ascii()
        );
        $response->headers->set('Content-Disposition', $disposition);

        return $response;
    }
}
