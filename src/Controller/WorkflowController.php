<?php

namespace App\Controller;

use App\Controller\Admin\DashboardController;
use App\Entity\Contact;
use App\Form\WorkflowTransitionType;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PropertyAccess\PropertyAccess;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Workflow\Exception\ExceptionInterface;
use Symfony\Component\Workflow\Registry;

class WorkflowController extends AbstractController
{
    public function __construct(private readonly EntityManagerInterface $entityManager,
        private readonly Registry $workflow,
        private readonly AdminUrlGenerator $adminUrlGenerator)
    {
    }

    #[Route(path: '/workflow/apply-transition-form/{entity}/{id}/{transition}/{workflowName}', name: 'app_workflow_apply_transition_with_form', requirements: ['entity' => '[a-zA-Z0-9_]+', 'id' => '\d+|[\w:_/+-]+', 'transition' => '[a-zA-Z0-9_.]+', 'workflowName' => '[a-zA-Z0-9_.]+'], defaults: ['workflowName' => null])]
    public function applyTransitionWithForm(string $entity, string $id, string $transition, ?string $workflowName, Request $request): RedirectResponse|Response
    {
        $object = $this->getObject($entity, $id);
        $this->denyAccessUnlessGranted('EDIT', $object);
        $targetUrl = $this->getTargetUrl($request, $entity, $id);
        $form = $this->createForm(WorkflowTransitionType::class);
        $form->handleRequest($request);
        if ($form->isSubmitted() && !$form->isValid()) {
            $errors = [];
            foreach ($form->getErrors(true) as $error) {
                $errors[] = $error->getMessage();
            }
            $this->addFlash('danger', implode(', ', $errors));

            return $this->redirect($targetUrl);
        }
        if ($form->isSubmitted() && $form->isValid()) {
            if ($form->has('note')) {
                $accessor = PropertyAccess::createPropertyAccessor();
                $accessor->setValue($object, 'setWorkflowLogNote', $form->get('note')->getData());
            }

            $this->makeTransition($object, $transition, $workflowName);

            return $this->redirect($targetUrl);
        }

        return $this->render('workflow/apply_transition_form.html.twig', [
            'entity' => $entity,
            'id' => $id,
            'transition' => $transition,
            'workflowName' => $workflowName,
            'targetUrl' => $targetUrl,
            'form' => $form->createView(),
        ]);
    }

    #[Route(path: '/workflow/apply-transition/{entity}/{id}/{transition}/{workflowName}', name: 'app_workflow_apply_transition', requirements: ['entity' => '[a-zA-Z0-9_]+', 'id' => '\d+|[\w:_/+-]+', 'transition' => '[a-zA-Z0-9_.]+', 'workflowName' => '[a-zA-Z0-9_.]+'], defaults: ['workflowName' => null])]
    public function applyTransition(string $entity, string $id, string $transition, ?string $workflowName, Request $request): RedirectResponse
    {
        $object = $this->getObject($entity, $id);
        $this->denyAccessUnlessGranted('EDIT', $object);
        $object = $this->getObject($entity, $id);
        $targetUrl = $this->getTargetUrl($request, $entity, $id);
        $this->makeTransition($object, $transition, $workflowName);

        return $this->redirect($targetUrl);
    }

    private function makeTransition($object, string $transition, ?string $workflowName)
    {
        try {
            $workflow = $this->workflow->get($object, $workflowName);
            $workflow->apply($object, $transition);
            $this->entityManager->flush();

            $this->addFlash('success', 'workflow.transition.apply_success');
        } catch (ExceptionInterface $e) {
            $this->addFlash('danger', $e->getMessage());
        }
    }

    private function getObject(string $entity, $id)
    {
        $object = match ($entity) {
            'contact' => $this->entityManager->getRepository(Contact::class)->find($id),
            default => null,
        };

        if (!$object) {
            throw $this->createNotFoundException('Object not found.');
        }

        return $object;
    }

    private function getTargetUrl(Request $request, $entity, $id): string
    {
        if (!empty($request->query->get('targetUrl'))) {
            return (string) $request->query->get('targetUrl');
        }

        return $this->adminUrlGenerator
            ->setDashboard(DashboardController::class)
            ->generateUrl();
    }
}
