<?php

namespace App\Controller;

use App\Entity\Category;
use App\Entity\Page;
use App\Repository\PageRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class PageController extends AbstractController
{
    #[Route(path: '/{slug}_c{id}/{page}', name: 'app_page_list', requirements: ['slug' => '.+', 'id' => '\d+', 'page' => '\d*'], defaults: ['page' => 1])]
    public function list(Category $category, int $page, PageRepository $pageRepository): Response
    {
        $pages = $pageRepository->listByCategory($category, $page);

        return $this->render('category/list.html.twig', [
            'pages' => $pages,
            'category' => $category,
        ]);
    }

    #[Route(path: '/{slug}_p{id}.html', name: 'app_page_info', requirements: ['slug' => '.+', 'id' => '\d+'])]
    public function show(Page $page): Response
    {
        if (!$page->isEnabled()) {
            throw $this->createNotFoundException("Page isn't found.");
        }

        return match($page->getTemplate()) {
            Page::TEMPLATE_NEWS => $this->render('page/show_news.html.twig', ['page' => $page]),
            default => $this->render('page/show.html.twig', ['page' => $page]),
        };
    }

    #[Route(path: 'tags/{tag}/search/{page}', name: 'app_page_list_by_tag', requirements: ['tag' => 'INFO|TOP', 'page' => '\d*'], options: ['utf8' => true], defaults: ['page' => 1])]
    public function pageListByTags(string $tag, int $page, PageRepository $pageRepository): Response
    {
        $pages = $pageRepository->listByTag($tag, $page);

        return $this->render('category/list_by_tag.html.twig', ['pages' => $pages, 'tag' => $tag]);
    }
}
