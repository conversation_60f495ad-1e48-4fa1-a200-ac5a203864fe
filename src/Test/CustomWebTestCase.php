<?php

namespace App\Test;

use App\Entity\User;
use App\Repository\UserRepository;
use libphonenumber\NumberParseException;
use libphonenumber\PhoneNumber;
use libphonenumber\PhoneNumberUtil;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class CustomWebTestCase extends WebTestCase
{
    protected function loginAdminUser(KernelBrowser $client, string $email = '<EMAIL>'): User
    {
        $userRepository = static::getContainer()->get(UserRepository::class);

        // retrieve the test user
        $adminUser = $userRepository->findOneByEmail($email);

        // simulate $adminUser being logged in
        $client->loginUser($adminUser);

        return $adminUser;
    }

    protected function loginUser(KernelBrowser $client, string $email = '<EMAIL>'): User
    {
        $userRepository = static::getContainer()->get(UserRepository::class);

        // retrieve the test user
        $user = $userRepository->findOneByEmail($email);

        // simulate $user being logged in
        $client->loginUser($user);

        return $user;
    }

    protected function generatePhoneNumber(): ?PhoneNumber
    {
        $phoneUtil = PhoneNumberUtil::getInstance();
        try {
            return $phoneUtil->parse('+359887100000');
        } catch (NumberParseException) {
            return null;
        }
    }
}
