<?php

namespace App\Service;

use League\Flysystem\FilesystemException;
use League\Flysystem\FilesystemOperator;
use League\Flysystem\UnableToDeleteFile;
use League\Flysystem\UnableToReadFile;
use League\Flysystem\UnableToWriteFile;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\File\File;

class UploaderHelper
{
    final public const CONSULTANT_FILE = 'consultant_file';
    final public const APPOINTMENT_FILE = 'appointment_file';
    final public const WEB_FILE = 'web_file';
    final public const OTHER_FILE = 'other_file';

    public function __construct(private readonly FilesystemOperator $defaultStorage, private readonly LoggerInterface $logger)
    {
    }

    /**
     * @throws UnableToWriteFile
     * @throws FilesystemException
     */
    public function upload(File $file, ?string $existingFilePath, string $path = self::OTHER_FILE): string
    {
        $newFilename = date('YmdHis').'-'.uniqid().'.'.$file->guessExtension();

        $stream = fopen($file->getPathname(), 'r');
        $this->defaultStorage->writeStream($path.'/'.$newFilename, $stream);
        if (is_resource($stream)) {
            fclose($stream);
        }

        if ($existingFilePath) {
            try {
                $this->defaultStorage->delete($existingFilePath);
            } catch (FilesystemException|UnableToDeleteFile) {
                $this->logger->alert(sprintf('Old uploaded file "%s" was missing when trying to delete', $existingFilePath));
            }
        }

        return $path.'/'.$newFilename;
    }

    /**
     * @throws UnableToReadFile
     * @throws FilesystemException
     */
    public function read(string $path): string
    {
        return $this->defaultStorage->read($path);
    }

    /**
     * @return resource
     *
     * @throws UnableToReadFile
     * @throws FilesystemException
     */
    public function readStream(string $path)
    {
        return $this->defaultStorage->readStream($path);
    }

    /**
     * @throws UnableToDeleteFile
     * @throws FilesystemException
     */
    public function delete(string $path)
    {
        $this->defaultStorage->delete($path);
    }
}
