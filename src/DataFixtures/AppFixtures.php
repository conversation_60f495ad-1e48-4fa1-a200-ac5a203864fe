<?php

namespace App\DataFixtures;

use App\Entity\Category;
use App\Entity\Page;
use App\Entity\PageSection;
use App\Factory\CategoryFactory;
use App\Factory\PageFactory;
use App\Factory\PageSectionFactory;
use App\Factory\UserFactory;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

class AppFixtures extends Fixture
{
    public function load(ObjectManager $manager): void
    {
        $beginAt = new \DateTime();

        // Admin user
        UserFactory::new()
            ->adminUser('<EMAIL>', '12345')
            ->create()
        ;

        // Category
        foreach ($this->getCategoryAttributes() as $attributes) {
            CategoryFactory::new($attributes)->create();
        }

        // Product
        foreach ($this->getProductAttributes() as $attributes) {
            PageFactory::new($attributes)->create([
                'categories' => CategoryFactory::randomRange(1, 1, ['name' => 'Продукти']),
            ]);
        }

        // Pages - HOMEPAGE_FAQ
        PageFactory::new()
            ->withImage()
            ->create([
                'name' => 'Често задавани въпроси',
                'categories' => CategoryFactory::randomRange(1, 1, ['name' => 'Начало']),
                'tags' => [Page::TAG_HOMEPAGE_FAQ],
                'sections' => PageSectionFactory::new(['type' => PageSection::TYPE_FAQ])->many(5),
            ]);

        // Pages - HOMEPAGE_GALLERY
        PageFactory::new()
            ->withImage()
            ->create([
                'name' => 'BATCOS',
                'shortDescription' => 'BATCOS Forever Luxury - Композитни декинг дъски,​ Композитен сайдинг, Композитен декоративен ъгъл, Композитен профил за монтажна скара',
                'categories' => CategoryFactory::randomRange(1, 1, ['name' => 'Начало']),
                'tags' => [Page::TAG_HOMEPAGE_GALLERY],
                'sections' => PageSectionFactory::new(['type' => PageSection::TYPE_GALLERY])->many(5),
            ]);

        // Pages - HOMEPAGE_NEWS
        PageFactory::new()
            ->withImage()
            ->many(3)
            ->create([
                'categories' => CategoryFactory::randomRange(1, 1, ['name' => 'Монтаж']),
                'tags' => [Page::TAG_HOMEPAGE_NEWS],
            ]);    

        PageFactory::new()
            ->withImage()
            ->many(5)
            ->create(['tags' => []]);
        PageFactory::new()
            ->withImage()
            ->withPageSection()
            ->withPageRelation()
            ->many(5)
            ->create(['tags' => []]);

        $endAt = new \DateTime();
        $interval = $beginAt->diff($endAt);
        dump($interval->format('%h hours, %i minutes, %s seconds'));
    }

    private function getCategoryAttributes(): array
    {
        return [
            ['name' => 'Начало', 'tags' => [Category::TAG_TOP], 'routeToPath' => 'app_homepage', 'isActive' => true, 'position' => 1],
            ['name' => 'Продукти', 'tags' => [Category::TAG_TOP, Category::TAG_FOOTER_1], 'position' => 2],
            ['name' => 'Монтаж', 'tags' => [Category::TAG_TOP], 'position' => 3],
            ['name' => 'Цени', 'tags' => [Category::TAG_TOP], 'position' => 3],
            ['name' => 'Контакти', 'tags' => [Category::TAG_TOP], 'routeToPath' => 'app_contact', 'position' => 6],

            ['name' => 'Въпроси', 'tags' => [Category::TAG_FOOTER_2]],
            ['name' => 'Политика за поверителност', 'tags' => [Category::TAG_FOOTER_2]],
            ['name' => 'Общи условия', 'tags' => [Category::TAG_FOOTER_2]],

            ['name' => 'Facebook', 'tags' => [Category::TAG_SOCIAL], 'icon' => 'ic:round-facebook', 'routeToUrl' => 'https://www.facebook.com/decking.batcos/'],
            ['name' => 'Instagram', 'tags' => [Category::TAG_SOCIAL], 'icon' => 'mdi:instagram', 'routeToUrl' => 'https://www.instagram.com/decking.batcos/'],
        ];
    }

    private function getProductAttributes(): array
    {
        return [
            ['name' => 'Композитен декинг "Водна сила"', 'tags' => [Page::TAG_DECKING], 'isActive' => true, 'position' => 1],
            ['name' => 'Композитен декинг  "Великолепие" ', 'tags' => [Page::TAG_DECKING], 'isActive' => true, 'position' => 2],
            ['name' => 'Композитен декинг  "Хармония"', 'tags' => [Page::TAG_DECKING], 'isActive' => true, 'position' => 3],
            ['name' => 'Композитен профил за конструкция ', 'tags' => [Page::TAG_DECKING_ACCESSORY], 'isActive' => true, 'position' => 4],
            ['name' => 'Композитен прав ъгъл за чела и вертикали', 'tags' => [Page::TAG_DECKING_ACCESSORY], 'isActive' => true, 'position' => 5],
            ['name' => 'Челна клипса от неръждаема стомана с винт', 'tags' => [Page::TAG_DECKING_ACCESSORY], 'isActive' => true, 'position' => 6],
            ['name' => 'Основна клипса от неръждаема стомана с винт ', 'tags' => [Page::TAG_DECKING_ACCESSORY], 'isActive' => true, 'position' => 7],

            ['name' => 'Сайдинг "Възрожденска къща" ', 'tags' => [Page::TAG_SIDING], 'isActive' => true, 'position' => 1],
            ['name' => 'Сайдинг "Модерна фасада 3D " ', 'tags' => [Page::TAG_SIDING], 'isActive' => true, 'position' => 2],
            ['name' => 'Подложен профил за конструкция от ДПК', 'tags' => [Page::TAG_SIDING_ACCESSORY], 'isActive' => true, 'position' => 3],
            ['name' => 'Завършващ прав ъгъл ', 'tags' => [Page::TAG_SIDING_ACCESSORY], 'isActive' => true, 'position' => 4],
            ['name' => 'Неръждаем винт за монтаж на сайдинг ', 'tags' => [Page::TAG_SIDING_ACCESSORY], 'isActive' => true, 'position' => 5],
        ];
    }
}
