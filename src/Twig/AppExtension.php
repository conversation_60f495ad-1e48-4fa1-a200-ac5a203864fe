<?php

namespace App\Twig;

use Twig\Extension\AbstractExtension;
use Twig\Markup;
use Twig\TwigFilter;
use Twig\TwigFunction;

class AppExtension extends AbstractExtension
{
    private ?\Parsedown $parsedown = null;

    public function getFilters(): array
    {
        return [
            // If your filter generates SAFE HTML, you should add a third
            // parameter: ['is_safe' => ['html']]
            // Reference: https://twig.symfony.com/doc/2.x/advanced.html#automatic-escaping
            new TwigFilter('sha1_encode', $this->sha1EncodeFilter(...)),
            new TwigFilter('markdown', $this->markdownFilter(...)),
        ];
    }

    public function getFunctions(): array
    {
        return [
            new TwigFunction('workflow__getPlaces', [WorkflowRuntime::class, 'getPlaces']),
            new TwigFunction('workflow__getTransitions', [WorkflowRuntime::class, 'getTransitions']),

            new TwigFunction('category__childrenHierarchy', [CategoryRuntime::class, 'childrenHierarchy']),
            new TwigFunction('category__getChildren', [CategoryRuntime::class, 'getChildren']),
            new TwigFunction('category__getRootCategories', [CategoryRuntime::class, 'getRootCategories']),
            new TwigFunction('category__getPath', [CategoryRuntime::class, 'getPath']),
            new TwigFunction('category__getUrl', [CategoryRuntime::class, 'getUrl']),
            new TwigFunction('category__findByTag', [CategoryRuntime::class, 'findByTag']),

            new TwigFunction('page__findByTag', [PageRuntime::class, 'findByTag']),
            new TwigFunction('page__findByCategory', [PageRuntime::class, 'findByCategory']),
            new TwigFunction('page__findByCategoryTag', [PageRuntime::class, 'findByCategoryTag']),
            new TwigFunction('page__findByTemplate', [PageRuntime::class, 'findByTemplate']),

            new TwigFunction('document__findByType', [DocumentRuntime::class, 'findByType']),

            new TwigFunction('routeToPath__generate', [RouteToPathRuntime::class, 'generate']),
        ];
    }

    public function sha1EncodeFilter($str)
    {
        return hash('sha1', (string) $str);
    }

    public function markdownFilter(?string $text)
    {
        return empty($text) ? '' : new Markup($this->getParsedown()->parse($text), 'UTF-8');
    }

    private function getParsedown(): \Parsedown
    {
        if (!$this->parsedown) {
            $this->parsedown = new \Parsedown();
        }

        return $this->parsedown;
    }
}
