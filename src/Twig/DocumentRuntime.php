<?php

namespace App\Twig;

use Symfony\Component\PropertyAccess\PropertyAccess;
use Twig\Extension\RuntimeExtensionInterface;

class DocumentRuntime implements RuntimeExtensionInterface
{
    public function findByType($documents, string $method, string $values): array
    {
        if (!is_array($values)) {
            $values = [$values];
        }

        $accessor = PropertyAccess::createPropertyAccessor();
        $filterArr = [];
        foreach ($documents as $document) {
            if (in_array($accessor->getValue($document, $method), $values)) {
                $filterArr[] = $document;
            }
        }

        return $filterArr;
    }
}
