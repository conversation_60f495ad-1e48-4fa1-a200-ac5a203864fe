<?php

namespace App\Twig;

use Symfony\Component\Workflow\Registry;
use Twig\Extension\RuntimeExtensionInterface;

class WorkflowRuntime implements RuntimeExtensionInterface
{
    public function __construct(private readonly Registry $workflowRegistry)
    {
    }

    public function getPlaces($object)
    {
        $workflow = $this->workflowRegistry->get($object);

        return $workflow->getDefinition()->getPlaces();
    }

    public function getTransitions($object)
    {
        $workflow = $this->workflowRegistry->get($object);

        return $workflow->getDefinition()->getTransitions();
    }
}
