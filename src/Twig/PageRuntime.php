<?php

namespace App\Twig;

use App\Entity\Category;
use App\Repository\PageRepository;
use Twig\Extension\RuntimeExtensionInterface;

class PageRuntime implements RuntimeExtensionInterface
{
    public function __construct(private readonly PageRepository $pageRepository)
    {
    }

    public function findByTag(string $tag, int $maxResults)
    {
        return $this->pageRepository->findByTag($tag, $maxResults);
    }

    public function findByCategory(Category $category, int $maxResults)
    {
        return $this->pageRepository->findByCategory($category, $maxResults);
    }

    public function findByCategoryTag(string $tag, int $maxResults)
    {
        return $this->pageRepository->findByCategoryTag($tag, $maxResults);
    }

    public function findByTemplate(string $template, int $maxResults)
    {
        return $this->pageRepository->findByTemplate($template, $maxResults);
    }
}
