<?php

namespace App\Twig;

use App\Entity\Category;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Query;
use Gedmo\Tree\Hydrator\ORM\TreeObjectHydrator;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Contracts\Cache\CacheInterface;
use Symfony\Contracts\Cache\ItemInterface;
use Twig\Extension\RuntimeExtensionInterface;

class CategoryRuntime implements RuntimeExtensionInterface
{
    public function __construct(private readonly EntityManagerInterface $entityManager, private readonly RouterInterface $router, private readonly RequestStack $requestStack, private readonly CacheInterface $cache)
    {
    }

    public function getRootCategories()
    {
        $locale = $this->requestStack->getMainRequest()->getLocale();
        $cacheKey = sprintf('%s_%s', Category::CACHE_KEY_TREES, $locale);

        return $this->cache->get($cacheKey, function (ItemInterface $item) {
            $item->expiresAfter(24 * 60 * 60);
            $this->entityManager->getConfiguration()->addCustomHydrationMode('tree', TreeObjectHydrator::class);

            return $this->entityManager->getRepository(Category::class)
                ->createQueryBuilder('c')
                ->where('c.isActive = 1')
                ->orderBy('c.position', 'ASC')
                ->getQuery()
                ->setHint(Query::HINT_INCLUDE_META_COLUMNS, true)
                ->getResult('tree');
        });
    }

    public function getPath(Category $category)
    {
        return $this->entityManager->getRepository(Category::class)->getPath($category);
    }

    public function getChildren(?Category $category = null)
    {
        return $this->entityManager->getRepository(Category::class)->getChildren($category);
    }

    public function childrenHierarchy(?Category $category = null)
    {
        return $this->entityManager->getRepository(Category::class)->childrenHierarchy($category);
    }

    public function getUrl(Category $category, int $referenceType = UrlGeneratorInterface::ABSOLUTE_PATH)
    {
        if ($category->getRouteToPage()) {
            return $this->router->generate('app_page_info', [
                'slug' => $category->getRouteToPage()->getSlug(),
                'id' => $category->getRouteToPage()->getId(),
            ], $referenceType);
        } elseif ($category->getRouteToPath()) {
            return $this->router->generate($category->getRouteToPath(), [], $referenceType);
        } elseif ($category->getRouteToUrl()) {
            return $category->getRouteToUrl();
        }

        return $this->router->generate('app_page_list', [
            'slug' => $category->getSlug(),
            'id' => $category->getId(),
        ], $referenceType);
    }

    public function findByTag(string $tag, ?int $maxResults = null)
    {
        return $this->entityManager->getRepository(Category::class)->findByTag($tag, $maxResults);
    }

    public function hasCategoryChildrenWithTag(Category $category, string $tag): bool
    {
        foreach ($category->getChildren() as $link) {
            if (in_array($tag, $link->getTags())) {
                return true;
            }
        }

        return false;
    }
}
