<?php

namespace App\Twig;

use Psr\Log\LoggerInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Twig\Extension\RuntimeExtensionInterface;

class RouteToPathRuntime implements RuntimeExtensionInterface
{
    public function __construct(private readonly UrlGeneratorInterface $router, private readonly LoggerInterface $logger)
    {
    }

    public function generate(string $value): string
    {
        try {
            $segments = explode(';', $value);
            $name = array_shift($segments);
            $jsonParameters = array_shift($segments);

            if ($jsonParameters) {
                $parameters = json_decode($jsonParameters, true, 512, JSON_THROW_ON_ERROR);
            } else {
                $parameters = [];
            }

            return $this->router->generate($name, $parameters);
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage(), ['value' => $value]);
        }

        return '';
    }
}
