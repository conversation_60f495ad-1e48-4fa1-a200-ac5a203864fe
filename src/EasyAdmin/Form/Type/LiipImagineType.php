<?php

namespace App\EasyAdmin\Form\Type;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class LiipImagineType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'filter' => null,
        ]);

        $resolver
            ->setAllowedTypes('filter', 'string')
            ->setRequired('filter');
    }
}
