<?php

namespace App\EasyAdmin\Form\Type;

use App\Entity\Document;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class DocumentType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('originator', HiddenType::class, ['data' => $options['originator']])
            ->add('fileUploadedFile', FileType::class, ['label' => 'document.form.fileUploadedFile'])
        ;

        if ($options['showName']) {
            $builder->add('name', null, ['label' => 'document.form.name']);
        }

        if ($options['showAccessType']) {
            $builder->add('accessType', null, ['label' => 'document.form.accessType']);
        }
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'originator' => null,
            'showName' => false,
            'showAccessType' => false,
            'data_class' => Document::class,
        ]);

        $resolver
            ->setAllowedTypes('showName', 'boolean')
            ->setAllowedTypes('showAccessType', 'boolean')
            ->setAllowedTypes('originator', ['string', 'null'])
            ->setRequired('originator');
    }
}
