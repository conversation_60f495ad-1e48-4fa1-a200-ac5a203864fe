<?php

namespace App\EasyAdmin\Field;

use EasyCorp\Bundle\EasyAdminBundle\Contracts\Field\FieldInterface;
use EasyCorp\Bundle\EasyAdminBundle\Field\FieldTrait;
use Yokai\EnumBundle\Form\Type\EnumType;

class <PERSON>kaiEnumField implements FieldInterface
{
    use FieldTrait;

    public const OPTION_ENUM = 'enum';
    public const OPTION_ALLOW_MULTIPLE_CHOICES = 'allowMultipleChoices';
    public const OPTION_BACKGROUND_COLOR = 'backgroundColor';
    public const OPTION_TEXT_COLOR = 'textColor';

    /**
     * @param string|false|null $label
     */
    public static function new(string $propertyName, $label = null): self
    {
        return (new self())
            ->setProperty($propertyName)
            ->setLabel($label)

            // this template is used in 'index' and 'detail' pages
            ->setTemplatePath('admin/field/enum.html.twig')

            // this is used in 'edit' and 'new' pages to edit the field contents
            // you can use your own form types too
            ->setFormType(EnumType::class)
            ->setCustomOption(self::OPTION_ENUM, null)
            ->setCustomOption(self::OPTION_ALLOW_MULTIPLE_CHOICES, false)
            ->setCustomOption(self::OPTION_BACKGROUND_COLOR, null)
            ->setCustomOption(self::OPTION_TEXT_COLOR, null)
        ;
    }

    public function setEnum(string $enum): self
    {
        $this
            ->setFormTypeOption('enum', $enum)
            ->setCustomOption(self::OPTION_ENUM, $enum);

        return $this;
    }

    public function allowMultipleChoices(bool $allow = true): self
    {
        $this
            ->setCustomOption(self::OPTION_ALLOW_MULTIPLE_CHOICES, $allow)
            ->setFormTypeOption('multiple', true)
            ->setFormTypeOption('expanded', true);

        return $this;
    }

    public function setBackgroundColor(?string $backgroundColor): self
    {
        $this->setCustomOption(self::OPTION_BACKGROUND_COLOR, $backgroundColor);

        return $this;
    }

    public function setTextColor(?string $textColor): self
    {
        $this->setCustomOption(self::OPTION_TEXT_COLOR, $textColor);

        return $this;
    }
}
