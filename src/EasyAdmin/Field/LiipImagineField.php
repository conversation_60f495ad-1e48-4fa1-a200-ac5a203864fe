<?php

namespace App\EasyAdmin\Field;

use App\EasyAdmin\Form\Type\LiipImagineType;
use EasyCorp\Bundle\EasyAdminBundle\Contracts\Field\FieldInterface;
use EasyCorp\Bundle\EasyAdminBundle\Field\FieldTrait;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Contracts\Translation\TranslatableInterface;

class LiipImagineField implements FieldInterface
{
    use FieldTrait;

    public const OPTION_FILTER = 'filter';
    public const OPTION_DOWNLOAD_ROUTE = 'download_route';

    /**
     * @param TranslatableInterface|string|false|null $label
     */
    public static function new(string $propertyName, $label = null): self
    {
        return (new self())
            ->setProperty($propertyName)
            ->setLabel($label)

            // this template is used in 'index' and 'detail' pages
            ->setTemplatePath('admin/field/liip_imagine.html.twig')
            ->addFormTheme('admin/form/liip_imagine.html.twig')

            // this is used in 'edit' and 'new' pages to edit the field contents
            ->setFormType(LiipImagineType::class)
            ->setFormTypeOption('filter', 'thumbnail_sm')
            ->setCustomOption(self::OPTION_FILTER, 'thumbnail_sm')
            ->setCustomOption(self::OPTION_DOWNLOAD_ROUTE, '')
        ;
    }

    public function setFilter(string $filter): self
    {
        $this
            ->setCustomOption(self::OPTION_FILTER, $filter)
            ->setFormTypeOption('filter', $filter);

        return $this;
    }

    public function setDownloadRoute(string $route): self
    {
        $this->setCustomOption(self::OPTION_DOWNLOAD_ROUTE, $route);

        return $this;
    }
}
