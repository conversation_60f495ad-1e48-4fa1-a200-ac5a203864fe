<?php

namespace App\EasyAdmin\Field\Configurator;

use App\EasyAdmin\Field\YokaiEnumField;
use EasyCorp\Bundle\EasyAdminBundle\Context\AdminContext;
use EasyCorp\Bundle\EasyAdminBundle\Contracts\Field\FieldConfiguratorInterface;
use EasyCorp\Bundle\EasyAdminBundle\Dto\EntityDto;
use EasyCorp\Bundle\EasyAdminBundle\Dto\FieldDto;
use Yokai\EnumBundle\EnumRegistry;

final class YokaiEnumConfigurator implements FieldConfiguratorInterface
{
    public function __construct(private readonly EnumRegistry $enumRegistry)
    {
    }

    public function supports(FieldDto $field, EntityDto $entityDto): bool
    {
        return YokaiEnumField::class === $field->getFieldFqcn();
    }

    public function configure(FieldDto $field, EntityDto $entityDto, AdminContext $context): void
    {
        $enum = $field->getCustomOption('enum');
        if (null === $enum) {
            throw new \InvalidArgumentException(sprintf('The "%s" enum field must define its possible enum using the setEnum() method.', $field->getProperty()));
        }

        if (!$this->enumRegistry->has($enum)) {
            throw new \InvalidArgumentException(sprintf('The "%s" value used as the enum class of the "%s" field is not a valid enum.', $enum, $field->getProperty()));
        }

        $field->setFormTypeOption('enum', $enum);
    }
}
