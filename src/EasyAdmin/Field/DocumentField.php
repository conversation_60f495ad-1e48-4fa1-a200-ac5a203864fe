<?php

namespace App\EasyAdmin\Field;

use App\EasyAdmin\Form\Type\DocumentType;
use EasyCorp\Bundle\EasyAdminBundle\Contracts\Field\FieldInterface;
use EasyCorp\Bundle\EasyAdminBundle\Field\FieldTrait;

class DocumentField implements FieldInterface
{
    use FieldTrait;

    public const OPTION_ORIGINATOR = 'originator';
    public const OPTION_NAME = 'showName';
    public const OPTION_ACCESS_TYPE = 'showAccessType';

    /**
     * @param string|false|null $label
     */
    public static function new(string $propertyName, $label = null): self
    {
        return (new self())
            ->setProperty($propertyName)
            ->setLabel($label)

            // this template is used in 'index' and 'detail' pages
            ->setTemplatePath('admin/field/document.html.twig')

            // this is used in 'edit' and 'new' pages to edit the field contents
            // you can use your own form types too
            ->setFormType(DocumentType::class)
            ->setCustomOption(self::OPTION_ORIGINATOR, null)
            ->setCustomOption(self::OPTION_NAME, false)
            ->setCustomOption(self::OPTION_ACCESS_TYPE, false)
        ;
    }

    public function setOriginator(string $originator): self
    {
        $this->setFormTypeOption(self::OPTION_ORIGINATOR, $originator);

        return $this;
    }

    public function showName(): self
    {
        $this->setFormTypeOption(self::OPTION_NAME, true);

        return $this;
    }

    public function showAccessType(): self
    {
        $this->setFormTypeOption(self::OPTION_ACCESS_TYPE, true);

        return $this;
    }
}
