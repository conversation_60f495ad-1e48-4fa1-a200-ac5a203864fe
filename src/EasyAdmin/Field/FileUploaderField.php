<?php

namespace App\EasyAdmin\Field;

use EasyCorp\Bundle\EasyAdminBundle\Contracts\Field\FieldInterface;
use EasyCorp\Bundle\EasyAdminBundle\Field\FieldTrait;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Contracts\Translation\TranslatableInterface;

class FileUploader<PERSON>ield implements FieldInterface
{
    use FieldTrait;

    public const OPTION_PATH_FIELD = 'path_field';
    public const OPTION_MIME_FIELD = 'mime_field';
    public const OPTION_DELETE_ROUTE = 'delete_route';
    public const OPTION_DOWNLOAD_ROUTE = 'download_route';

    /**
     * @param TranslatableInterface|string|false|null $label
     */
    public static function new(string $propertyName, $label = null): self
    {
        return (new self())
            ->setProperty($propertyName)
            ->setLabel($label)

            // this template is used in 'index' and 'detail' pages
            ->setTemplatePath('admin/field/file_uploader.html.twig')
            ->addFormTheme('admin/form/file_uploader.html.twig')

            // this is used in 'edit' and 'new' pages to edit the field contents
            ->setFormType(FileType::class)
            // ->setFormTypeOption('field_name', $propertyName)
            ->setCustomOption(self::OPTION_PATH_FIELD, '')
            ->setCustomOption(self::OPTION_MIME_FIELD, '')
            ->setCustomOption(self::OPTION_DELETE_ROUTE, '')
            ->setCustomOption(self::OPTION_DOWNLOAD_ROUTE, '')
        ;
    }

    public function setPathField(string $field): self
    {
        $this->setCustomOption(self::OPTION_PATH_FIELD, $field);

        return $this;
    }

    public function setMimeField(string $field): self
    {
        $this->setCustomOption(self::OPTION_MIME_FIELD, $field);

        return $this;
    }

    public function setDeleteRoute(string $route): self
    {
        $this->setCustomOption(self::OPTION_DELETE_ROUTE, $route);

        return $this;
    }

    public function setDownloadRoute(string $route): self
    {
        $this->setCustomOption(self::OPTION_DOWNLOAD_ROUTE, $route);

        return $this;
    }
}
