<?php

namespace App\Security\Voter;

use App\Entity\User;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\User\UserInterface;

class UserAdminVoter extends Voter
{
    private const ATTRIBUTES = [
        Action::INDEX,
        Action::NEW,
        Action::EDIT,
        Action::DETAIL,
        Action::DELETE,
        Action::BATCH_DELETE,
        Action::SAVE_AND_ADD_ANOTHER,
        Action::SAVE_AND_CONTINUE,
        Action::SAVE_AND_RETURN,
    ];

    public function __construct(private readonly Security $security, private readonly AdminContextProvider $adminContextProvider)
    {
    }

    protected function supports($attribute, $subject): bool
    {
        // replace with your own logic
        // https://symfony.com/doc/current/security/voters.html
        return in_array($attribute, self::ATTRIBUTES)
            && User::class === $this->adminContextProvider->getContext()->getCrud()?->getEntityFqcn();
    }

    protected function voteOnAttribute($attribute, $subject, TokenInterface $token): bool
    {
        /** @var User|null $subject */
        /** @var User $user */
        $user = $token->getUser();
        // if the user is anonymous, do not grant access
        if (!$user instanceof UserInterface) {
            return false;
        }

        if ($this->security->isGranted('ROLE_ADMIN')) {
            return true;
        }

        return match ($attribute) {
            Action::INDEX,
            Action::NEW,
            Action::SAVE_AND_ADD_ANOTHER,
            Action::SAVE_AND_CONTINUE,
            Action::SAVE_AND_RETURN => $this->security->isGranted('ROLE_CONSULTANT'),

            Action::EDIT,
            Action::DETAIL => $subject instanceof User && $this->security->isGranted('EDIT', $subject),
            default => false,
        };
    }
}
