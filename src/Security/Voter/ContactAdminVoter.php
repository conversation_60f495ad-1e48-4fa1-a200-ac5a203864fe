<?php

namespace App\Security\Voter;

use App\Entity\Contact;
use App\Entity\User;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;

class ContactAdminVoter extends Voter
{
    private const ATTRIBUTES = [
        Action::INDEX,
        Action::NEW,
        Action::EDIT,
        Action::DETAIL,
        Action::DELETE,
        Action::BATCH_DELETE,
        Action::SAVE_AND_ADD_ANOTHER,
        Action::SAVE_AND_CONTINUE,
        Action::SAVE_AND_RETURN,
    ];

    public function __construct(private readonly Security $security, private readonly AdminContextProvider $adminContextProvider)
    {
    }

    protected function supports($attribute, $subject): bool
    {
        // replace with your own logic
        // https://symfony.com/doc/current/security/voters.html
        return in_array($attribute, self::ATTRIBUTES)
            && Contact::class === $this->adminContextProvider->getContext()->getCrud()?->getEntityFqcn();
    }

    protected function voteOnAttribute(string $attribute, $subject, TokenInterface $token): bool
    {
        $user = $token->getUser();
        // if the user is anonymous, do not grant access
        if (!$user instanceof User) {
            return false;
        }

        return match ($attribute) {
            Action::INDEX,
            Action::NEW,
            Action::EDIT,
            Action::DETAIL,
            Action::DELETE,
            Action::BATCH_DELETE,
            Action::SAVE_AND_ADD_ANOTHER,
            Action::SAVE_AND_CONTINUE,
            Action::SAVE_AND_RETURN, => $this->security->isGranted('ROLE_ADMIN'),
            default => false,
        };
    }
}
