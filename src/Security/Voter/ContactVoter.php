<?php

namespace App\Security\Voter;

use App\Entity\Contact;
use App\Entity\User;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\User\UserInterface;

class ContactVoter extends Voter
{
    private const ATTRIBUTES = ['CREATE', 'EDIT', 'VIEW', 'DELETE'];

    public function __construct(private readonly Security $security)
    {
    }

    public function supportsAttribute(string $attribute): bool
    {
        return in_array($attribute, self::ATTRIBUTES);
    }

    public function supportsType(string $subjectType): bool
    {
        // you can't use a simple BlogPost::class === $subjectType comparison
        // here because the given subject type could be the proxy class used
        // by Doctrine when creating the entity object
        return is_a($subjectType, Contact::class, true);
    }

    protected function supports($attribute, $subject): bool
    {
        // replace with your own logic
        // https://symfony.com/doc/current/security/voters.html
        return in_array($attribute, self::ATTRIBUTES)
            && $subject instanceof Contact;
    }

    protected function voteOnAttribute($attribute, $subject, TokenInterface $token): bool
    {
        /** @var Contact $subject */

        if ('CREATE' === $attribute) {
            return true;
        }

        /** @var User $user */
        $user = $token->getUser();
        // if the user is anonymous, do not grant access
        if (!$user instanceof UserInterface) {
            return false;
        }

        if ($this->security->isGranted('ROLE_ADMIN')) {
            return true;
        }

        return match ($attribute) {
            'EDIT', 'VIEW' => $this->security->isGranted('ROLE_ADMIN'),
            default => false,
        };
    }
}
