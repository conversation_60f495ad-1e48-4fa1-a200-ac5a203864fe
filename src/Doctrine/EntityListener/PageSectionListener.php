<?php

namespace App\Doctrine\EntityListener;

use App\Entity\PageSection;
use App\Service\UploaderHelper;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class PageSectionListener
{
    public function __construct(private readonly UploaderHelper $uploaderHelper, private readonly LoggerInterface $logger)
    {
    }

    public function prePersist(PageSection $pageSection)
    {
        $this->uploadFile($pageSection);
    }

    public function preUpdate(PageSection $pageSection, PreUpdateEventArgs $preUpdateEventArgs)
    {
        $this->uploadFile($pageSection);
    }

    public function postRemove(PageSection $pageSection)
    {
        $this->deleteFile($pageSection);
    }

    private function uploadFile(PageSection $pageSection): void
    {
        if ($pageSection->getFileUploadedFile() instanceof UploadedFile) {
            $pageSection->setFile(
                $this->uploaderHelper->upload($pageSection->getFileUploadedFile(), $pageSection->getFile(), UploaderHelper::WEB_FILE)
            );
        }
    }

    private function deleteFile(PageSection $pageSection): void
    {
        if (empty($pageSection->getFile())) {
            return;
        }

        try {
            $this->uploaderHelper->delete($pageSection->getFile());
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }
    }
}
