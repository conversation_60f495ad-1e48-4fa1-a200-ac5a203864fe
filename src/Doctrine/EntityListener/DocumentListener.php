<?php

namespace App\Doctrine\EntityListener;

use App\Entity\Document;
use App\Service\UploaderHelper;
use League\Flysystem\FilesystemException;
use League\Flysystem\UnableToWriteFile;
use Symfony\Component\HttpFoundation\RequestStack;

class DocumentListener
{
    public function __construct(private readonly UploaderHelper $uploaderHelper, private readonly RequestStack $requestStack)
    {
    }

    public function prePersist(Document $document)
    {
        $this->uploadFiles($document);
    }

    public function preUpdate(Document $document)
    {
        $this->uploadFiles($document);
    }

    public function postRemove(Document $document)
    {
        if (empty($document->getFilePath())) {
            return;
        }

        try {
            $this->uploaderHelper->delete($document->getFilePath());
        } catch (\Throwable $e) {
            $this->requestStack->getSession()->getFlashBag()->add('error', $e->getMessage());
        }
    }

    private function uploadFiles(Document $document)
    {
        if ($document->getFileUploadedFile()) {
            try {
                $document->setFilePath($this->uploaderHelper->upload($document->getFileUploadedFile(), $document->getFilePath()));
                $document->setFileUploadedFile(null);
            } catch (FilesystemException|UnableToWriteFile $e) {
                $this->requestStack->getSession()->getFlashBag()->add('error', $e->getMessage());
            }
        }
    }
}
