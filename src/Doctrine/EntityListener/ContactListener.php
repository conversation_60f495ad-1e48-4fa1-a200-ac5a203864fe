<?php

namespace App\Doctrine\EntityListener;

use App\Entity\Contact;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Workflow\WorkflowInterface;

class ContactListener
{
    public function __construct(private readonly WorkflowInterface $contactStateMachine, private readonly EntityManagerInterface $entityManager)
    {
    }

    public function postPersist(Contact $contact): void
    {
        $transition = Contact::STATE_START;

        if ($this->contactStateMachine->can($contact, $transition)) {
            $this->contactStateMachine->apply($contact, $transition);
            $this->entityManager->flush();
        }
    }
}
