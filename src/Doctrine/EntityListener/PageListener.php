<?php

namespace App\Doctrine\EntityListener;

use App\Entity\Page;
use App\Service\UploaderHelper;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class PageListener
{
    public function __construct(private readonly UploaderHelper $uploaderHelper, private readonly LoggerInterface $logger)
    {
    }

    public function prePersist(Page $page)
    {
        $this->uploadFile($page);
    }

    public function preUpdate(Page $page)
    {
        $this->uploadFile($page);
    }

    public function postRemove(Page $page)
    {
        if (empty($page->getImage())) {
            return;
        }

        try {
            $this->uploaderHelper->delete($page->getImage());
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }
    }

    private function uploadFile(Page $page): void
    {
        if ($page->getImageUploadedFile() instanceof UploadedFile) {
            $page->setImage(
                $this->uploaderHelper->upload($page->getImageUploadedFile(), $page->getImage(), UploaderHelper::WEB_FILE)
            );
        }
    }
}
