<?php

namespace App\Doctrine\EntityListener;

use App\Entity\Category;
use App\Service\UploaderHelper;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Contracts\Cache\CacheInterface;

class CategoryListener
{
    public function __construct(private readonly UploaderHelper $uploaderHelper, private readonly LoggerInterface $logger, private readonly array $locales, private readonly CacheInterface $cache)
    {
    }

    public function prePersist(Category $category): void
    {
        $this->uploadFile($category);
        $this->clearMenuCache();
    }

    public function preUpdate(Category $category): void
    {
        $this->uploadFile($category);
        $this->clearMenuCache();
    }

    public function postRemove(Category $category): void
    {
        $this->deleteFile($category);
        $this->clearMenuCache();
    }

    private function uploadFile(Category $category): void
    {
        if ($category->getImageUploadedFile() instanceof UploadedFile) {
            $category->setImage(
                $this->uploaderHelper->upload($category->getImageUploadedFile(), $category->getImage(), UploaderHelper::WEB_FILE)
            );
        }
    }

    private function deleteFile(Category $category): void
    {
        if (empty($category->getImage())) {
            return;
        }

        try {
            $this->uploaderHelper->delete($category->getImage());
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }
    }

    private function clearMenuCache(): void
    {
        foreach ($this->locales as $locale) {
            $cacheKey = sprintf('%s_%s', Category::CACHE_KEY_TREES, $locale);
            $this->cache->delete($cacheKey);
        }
    }
}
