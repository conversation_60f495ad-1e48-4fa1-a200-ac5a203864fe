<?php

// ./vendor/bin/php-cs-fixer fix --config=.php-cs-fixer.dist.php

$finder = (new PhpCsFixer\Finder())
    ->in(__DIR__)
    ->exclude('var')
;

return (new PhpCsFixer\Config())
    ->setRules([
        '@Symfony' => true,
        'yoda_style' => false,
        'class_attributes_separation' => [
            'elements' => [
                'method' => 'one',
                'property' => 'one',
                'trait_import' => 'one',
            ],
        ],
    ])
    ->setFinder($finder)
;
