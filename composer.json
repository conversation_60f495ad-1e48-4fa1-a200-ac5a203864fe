{"type": "project", "license": "proprietary", "require": {"php": "^8.1", "ext-ctype": "*", "ext-iconv": "*", "babdev/pagerfanta-bundle": "^4.4", "doctrine/dbal": "3.9.1", "doctrine/doctrine-bundle": "^2.11", "doctrine/doctrine-migrations-bundle": "^3.3", "doctrine/orm": "^3.2", "easycorp/easyadmin-bundle": "^4.0", "erusev/parsedown": "^1.7", "gedmo/doctrine-extensions": "^3.18.0", "karser/karser-recaptcha3-bundle": "^0.1.27", "knplabs/knp-time-bundle": "^2.4.0", "league/flysystem-bundle": "^3.0", "liip/imagine-bundle": "^2.6", "odolbeau/phone-number-bundle": "^4.0", "pagerfanta/doctrine-orm-adapter": "^4.7", "pagerfanta/twig": "^4.7", "phpdocumentor/reflection-docblock": "^5.2", "stof/doctrine-extensions-bundle": "^1.11", "symfony/asset": "7.2.*", "symfony/asset-mapper": "7.3.*", "symfony/console": "7.3.*", "symfony/doctrine-messenger": "7.3.*", "symfony/dotenv": "7.3.*", "symfony/expression-language": "7.3.*", "symfony/flex": "^2.4", "symfony/form": "7.3.*", "symfony/framework-bundle": "7.3.*", "symfony/http-client": "7.3.*", "symfony/intl": "7.3.*", "symfony/mailer": "7.3.*", "symfony/messenger": "7.3.*", "symfony/monolog-bundle": "^3.1", "symfony/notifier": "7.3.*", "symfony/process": "7.3.*", "symfony/property-access": "7.3.*", "symfony/property-info": "7.3.*", "symfony/runtime": "7.3.*", "symfony/security-bundle": "7.3.*", "symfony/serializer": "7.3.*", "symfony/stimulus-bundle": "^2.19", "symfony/string": "7.3.*", "symfony/translation": "7.3.*", "symfony/twig-bundle": "7.3.*", "symfony/ux-icons": "^2.20", "symfony/validator": "7.3.*", "symfony/web-link": "7.3.*", "symfony/workflow": "7.3.*", "symfony/yaml": "7.3.*", "symfonycasts/tailwind-bundle": "^0.10.0", "tales-from-a-dev/flowbite-bundle": "^0.7.0", "twig/cssinliner-extra": "^3.0", "twig/extra-bundle": "^3.0", "twig/inky-extra": "^3.0", "twig/intl-extra": "^3.0", "twig/markdown-extra": "^3.13", "twig/string-extra": "^3.0", "twig/twig": "^3.0", "yokai/enum-bundle": "^5.0"}, "require-dev": {"dama/doctrine-test-bundle": "^8.0", "doctrine/doctrine-fixtures-bundle": "^3.5", "friendsofphp/php-cs-fixer": "^3.54", "phpstan/phpstan-doctrine": "^1.5", "phpunit/phpunit": "^9.5", "rector/rector": "^1.0.4", "symfony/browser-kit": "7.3.*", "symfony/css-selector": "7.3.*", "symfony/debug-bundle": "7.3.*", "symfony/maker-bundle": "^1.0", "symfony/phpunit-bridge": "7.3.*", "symfony/stopwatch": "7.3.*", "symfony/var-dumper": "7.3.*", "symfony/web-profiler-bundle": "7.3.*", "zenstruck/foundry": "^2.5", "zenstruck/mailer-test": "^1.4", "zenstruck/messenger-test": "^1.11"}, "config": {"preferred-install": {"*": "dist"}, "sort-packages": true, "allow-plugins": {"symfony/flex": true, "symfony/runtime": true}}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"paragonie/random_compat": "2.*", "symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php71": "*", "symfony/polyfill-php70": "*", "symfony/polyfill-php56": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "messenger:stop-workers": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd", "importmap:install": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"], "cs:fix": "vendor/bin/php-cs-fixer fix src --config=./.php-cs-fixer.dist.php"}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.3.*"}}, "repositories": [{"type": "vcs", "url": "https://github.com/ggabrovski/epaybg-bundle.git"}]}