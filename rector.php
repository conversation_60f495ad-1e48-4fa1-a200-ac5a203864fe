<?php

declare(strict_types=1);

use <PERSON>\Config\RectorConfig;
use <PERSON>\Php80\Rector\Class_\StringableForToStringRector;
use <PERSON>\Php81\Rector\Property\ReadOnlyPropertyRector;
use <PERSON>\Set\ValueObject\LevelSetList;
use <PERSON>\Symfony\Set\SymfonySetList;

// ./vendor/bin/rector process
return static function (RectorConfig $rectorConfig): void {
    // paths to refactor; solid alternative to CLI arguments
    $rectorConfig->paths([
        __DIR__.'/src',
        __DIR__.'/tests',
    ]);

    $rectorConfig->disableParallel();

    $rectorConfig->symfonyContainerXml(__DIR__.'/var/cache/dev/App_KernelDevDebugContainer.xml');

    $rectorConfig->importNames();
    $rectorConfig->importShortClasses(false);

    $rectorConfig->skip([
        StringableForToStringRector::class,
    ]);

    $rectorConfig->rule(ReadOnlyPropertyRector::class);

    $rectorConfig->sets([
        LevelSetList::UP_TO_PHP_83,
        SymfonySetList::SYMFONY_CODE_QUALITY,
        SymfonySetList::SYMFONY_CONSTRUCTOR_INJECTION,
        // FoundrySetList::UP_TO_FOUNDRY_2,
    ]);
};
