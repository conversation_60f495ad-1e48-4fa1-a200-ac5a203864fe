{# @var section \App\Entity\PageSection #}
{% set url = '' %}

{% if section.routeToPath|default('')|length > 0 %}
    {% set url = routeToPath__generate(section.routeToPath) %}
{% elseif section.routeToPage|default('')|length > 0 %}
    {% set url = path('app_page_info', {id: section.routeToPage.id, slug: section.routeToPage.slug}) %}
{% elseif section.routeToUrl|default('')|length > 0 %}
    {% set url = section.routeToUrl %}
{% endif %}

{% if url|length > 0  %}
    {% set label = section.routeLabel|default('button.view_more'|trans) %}
    <p class="mb-6">
        <a href="{{ url }}" class="inline-flex items-center text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm mx-1 px-5 py-2.5 text-center dark:focus:ring-primary-900">{{ label }}</a>
    </p>
{% endif %}