{% for section in sections %}
    {% if loop.first %}<div class="grid gap-8 sm:grid-cols-2 lg:grid-cols-3">{% endif %}
    <article class="p-4 bg-white rounded-lg border border-gray-200 shadow-md dark:bg-gray-800 dark:border-gray-700">
        {% if section.file is defined and section.file|length > 0 and 'image/' in section.fileMimeType %}
            <img class="mb-5 rounded-lg" src="{{ section.file|imagine_filter('card_image_thumbnail') }}" alt="{{ section.name }}">
        {% endif %}

        <h2 class="my-2 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">{{ section.name }}</h2>
        <p class="mb-4 font-light text-gray-500 dark:text-gray-400">{{ section.shortDescription }}</p>

        {% if section.documents|length > 0 %}
            <p class="mb-6">
                {{ include('page_section/documents.html.twig', {'documents': section.documents}, with_context = false) }}
            </p>
        {% endif %}

        {{ include('page_section/route_link.html.twig', {'section': section}, with_context = false) }}
    </article>
    {% if loop.last %}</div>{% endif %}
{% endfor %}
