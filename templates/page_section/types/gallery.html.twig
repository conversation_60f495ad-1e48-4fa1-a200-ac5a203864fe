<div class="grid grid-cols-2 md:grid-cols-3 gap-4">
    {% for section in sections %}
        {% if section.file is defined and section.file|length > 0 and 'image/' in section.fileMimeType %}
            <!-- Modal toggle -->
            <div>
                <img data-modal-target="gallery-modal-{{ section.id }}" data-modal-toggle="gallery-modal-{{ section.id }}" class="h-auto max-w-full rounded-lg" src="{{ section.file|imagine_filter('card_image_thumbnail') }}" alt="{{ section.name }}">
            </div>

            <!-- Main modal -->
            <div id="gallery-modal-{{ section.id }}" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                <div class="relative p-4 w-full max-w-4xl max-h-full">
                    <!-- Modal content -->
                    <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                        <!-- Modal header -->
                        <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                                {{ section.name }}
                            </h3>
                            <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="gallery-modal-{{ section.id }}">
                                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                                </svg>
                                <span class="sr-only">Close modal</span>
                            </button>
                        </div>
                        <!-- Modal body -->
                        <div class="p-4 md:p-5 space-y-4 flex justify-center">
                            <img class="h-auto max-w-full rounded-lg" src="{{ section.file|imagine_filter('thumbnail_xl') }}" alt="{{ section.name }}">
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
    {% endfor %}
</div>
