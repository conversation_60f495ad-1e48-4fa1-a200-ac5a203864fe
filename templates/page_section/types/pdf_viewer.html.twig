{% for section in sections %}
    {% if section.file is defined and section.file|length > 0 %}
        <section class="bg-white dark:bg-gray-900">
            <div class="py-8 mx-auto max-w-screen-xl sm:py-16">
                <div class="mx-auto max-w-screen-md text-center">
                    {% if section.name|length > 0 %}
                        <h2 class="mb-4 text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white">{{ section.name }}</h2>
                    {% endif %}
                    {% if section.shortDescription|length > 0 %}
                        <p class="mb-6 font-light text-gray-500 md:text-lg dark:text-gray-400">{{ section.shortDescription }}</p>
                    {% endif %}
                </div>

                <div class="my-1">
                    {% if 'application/pdf' == section.fileMimeType %}
                        <embed src="{{ path('download_file_page_section', {'id': section.id}) }}" width="100%" height="720" type="application/pdf">
                    {% endif %}
                </div>
            </div>
        </section>
    {% endif %}
{% endfor %}
