<section class="bg-white dark:bg-gray-900">
    <div class="py-8 mx-auto max-w-screen-xl sm:py-16">
        <div id="accordion-flush" data-accordion="collapse" data-active-classes="bg-white dark:bg-gray-900 text-gray-900 dark:text-white" data-inactive-classes="text-gray-500 dark:text-gray-400">
        {% for section in sections %}
            {# @var section \App\Entity\PageSection #}
            <h2 id="accordion-flush-heading-{{ loop.index }}">
                <button type="button" class="flex justify-between items-center py-5 w-full font-bold text-left text-gray-900 bg-white border-b border-gray-200 dark:border-gray-700 dark:bg-gray-900 dark:text-white" data-accordion-target="#accordion-flush-body-{{ loop.index }}" aria-expanded="true" aria-controls="accordion-flush-body-{{ loop.index }}">
                    <span>{{ section.name }}</span>
                    <svg data-accordion-icon="" class="w-6 h-6 rotate-180 shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                </button>
            </h2>
            <div id="accordion-flush-body-{{ loop.index }}" class="" aria-labelledby="accordion-flush-heading-{{ loop.index }}">
                <div class="py-5 border-b border-gray-200 dark:border-gray-700">
                    <p class="mb-2 text-gray-500 dark:text-gray-100">{{ section.shortDescription }}</p>
                </div>
            </div>
        {% endfor %}
        </div>
    </div>
</section>
