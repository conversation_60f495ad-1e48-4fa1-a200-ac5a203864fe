{% for section in sections %}
    {% if section.file is defined and section.file|length > 0 and 'video/' in section.fileMimeType %}
    <section class="bg-white dark:bg-gray-900">
        <div class="py-8 mx-auto max-w-screen-md text-center sm:py-16">
            {% if section.name|length > 0 %}
                <h2 class="mb-4 text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white">{{ section.name }}</h2>
            {% endif %}
            {% if section.shortDescription|length > 0 %}
                <p class="mb-6 font-light text-gray-500 md:text-lg dark:text-gray-400">{{ section.shortDescription }}</p>
            {% endif %}

            <video class="w-full h-auto max-w-full" width="600" height="350" controls preload="none" poster="">
                <source src="{{ path('download_file_page_section', {'id': section.id}) }}" type="video/mp4">
                Your browser does not support the video tag.
            </video>
        </div>
    </section>
    {% endif %}
{% endfor %}
