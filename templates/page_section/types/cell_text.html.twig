{% for section in sections %}
<section class="bg-white dark:bg-gray-900">
    <div class="py-8 px-4 mx-auto max-w-screen-md text-center sm:py-16 lg:px-6 ">
        {% if section.name|length > 0 %}
            <h2 class="mb-4 text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white">{{ section.name }}</h2>
        {% endif %}
        {% if section.shortDescription|length > 0 %}
            <p class="mb-6 font-light text-gray-500 md:text-lg dark:text-gray-400">{{ section.shortDescription }}</p>
        {% endif %}

        {% if section.documents|length > 0 %}
            <p class="mb-6">
                {{ include('page_section/documents.html.twig', {'documents': section.documents}, with_context = false) }}
            </p>
        {% endif %}

        {{ include('page_section/route_link.html.twig', {'section': section}, with_context = false) }}
    </div>
</section>
{% endfor %}