{% for section in sections %}
<section class="bg-white dark:bg-gray-900">
    <div class="py-8 px-4 mx-auto max-w-screen-xl sm:py-16 lg:px-6 ">
        {% if section.name|length > 0 %}
            <h2 class="mb-4 text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white">{{ section.name }}</h2>
        {% endif %}
        {% if section.shortDescription|length > 0 %}
            <p class="mb-6 font-light text-gray-500 md:text-lg dark:text-gray-400">{{ section.shortDescription }}</p>
        {% endif %}

        {% for document in section.documents %}
            {% if loop.first %}<ul class="list-disc list-inside marker:text-gray-600 dark:marker:text-white">{% endif%}
                <li>
                    <a href="{{ path('app_download_page_section_document', {'id': document.id}) }}" target="_blank" class="font-medium text-blue-600 dark:text-blue-500 hover:underline">
                        {{ document.name|length > 0 ? document.name : 'button.download'|trans }}
                    </a>
                </li>
            {% if loop.last %}</ul>{% endif%}
        {% endfor %}
    </div>
</section>
{% endfor %}