{% for section in sections %}
<section class="bg-white dark:bg-gray-900">
    <div class="gap-8 items-center py-8 sm:py-16 mx-auto max-w-screen-xl xl:gap-16 md:grid md:grid-cols-2">
        {% if section.file is defined and section.file|length > 0 and 'image/' in section.fileMimeType %}
            <img class="w-full rounded-xl" src="{{ section.file|imagine_filter('card_image_thumbnail') }}" alt="{{ section.name }}">
        {% endif %}
        <div class="mt-4 md:mt-0">
            {% if section.name|length > 0 %}
                <h2 class="mb-4 text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white">{{ section.name }}</h2>
            {% endif %}
            {% if section.shortDescription|length > 0 %}
                <p class="mb-6 font-light text-gray-500 md:text-lg dark:text-gray-400">{{ section.shortDescription }}</p>
            {% endif %}

            {% if section.documents|length > 0 %}
                {{ include('page_section/documents.html.twig', {'documents': section.documents}, with_context = false) }}
            {% endif %}

            {{ include('page_section/route_link.html.twig', {'section': section}, with_context = false) }}
        </div>
    </div>
</section>
{% endfor %}
