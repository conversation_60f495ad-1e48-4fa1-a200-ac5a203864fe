{% set lastType = null %}
{% set buffer = [] %}
{% for section in page.sections %}
    {# @var section \App\Entity\PageSection #}
    {% if lastType != section.type and buffer|length > 0 %}
        {# If buffer is not empty render it and reset. #}
        {{ include('page_section/render_type.html.twig', {'sections': buffer, 'type': lastType}, with_context = false) }}
        {% set buffer = [] %}
    {% endif %}

    {% set buffer = buffer|merge([section]) %}
    {% set lastType = section.type %}

    {# If this is the last item and buffer is not empty render it. #}
    {% if loop.last and buffer|length > 0 %}
        {{ include('page_section/render_type.html.twig', {'sections': buffer, 'type': lastType}, with_context = false) }}
    {% endif %}
{% endfor %}