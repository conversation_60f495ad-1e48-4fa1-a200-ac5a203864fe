{% extends 'base.html.twig' %}

{% block title %}{{ page.name }}{% endblock %}
{% block meta_description %}{{ page.shortDescription }}{% endblock %}

{% block breadcrumbs %}
    {% set breadcrumbs = [] %}
    {% if page.mainCategory %}
        {% set breadcrumbs = breadcrumbs|merge([{'name': page.mainCategory.name, 'href': category__getUrl(page.mainCategory) }]) %}
    {% endif %}
    {% set breadcrumbs = breadcrumbs|merge([{'name': page.name }]) %}
    {{ parent() }}
{% endblock %}

{% block body %}
<section class="bg-white dark:bg-gray-900">
    <div class="max-w-screen-xl px-4 py-8 mx-auto lg:px-6 sm:py-16 lg:py-24">
        <div class="px-4 mx-auto max-w-screen-xl lg:px-6 mb-8 lg:mb-16">
            <h2 class="mb-4 text-center text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white">{{ page.name }}</h2>
            <div class="prose dark:prose-invert mx-auto max-w-screen-xl">{{ page.description|markdown }}</div>
        </div>

        {{ include("page_section/render.html.twig", {'page': page}, with_context = false) }}
        {{ include("page_relation/list.html.twig", {'pageRelations': page.pageRelations}, with_context = false) }}
    </div>
</section>
{% endblock %}
