{% extends 'base.html.twig' %}

{% block title %}{{ page.name }}{% endblock %}
{% block meta_description %}{{ page.shortDescription }}{% endblock %}

{% block breadcrumbs %}
    {% set breadcrumbs = [] %}
    {% if page.mainCategory %}
        {% set breadcrumbs = breadcrumbs|merge([{'name': page.mainCategory.name, 'href': category__getUrl(page.mainCategory) }]) %}
    {% endif %}
    {% set breadcrumbs = breadcrumbs|merge([{'name': page.name }]) %}
    {{ parent() }}
{% endblock %}

{% block body %}
<section class="bg-white dark:bg-gray-900">
    <div class="max-w-screen-xl px-4 py-8 mx-auto lg:px-6 sm:py-16 lg:py-24">
        <h1 class="mb-1 text-3xl font-extrabold tracking-tight leading-none md:text-4xl xl:text-5xl dark:text-white">{{ page.name }}</h1>
        <section class="my-4 bg-white dark:bg-gray-900">
            <div class="grid max-w-screen-xl mx-auto lg:grid-cols-12">
                <div class="mt-4 lg:col-span-5">
                    {% if page.image|length > 0 %}
                        <img class="rounded-lg" src="{{ page.image|imagine_filter('card_image_thumbnail') }}" alt="{{ page.name }}">
                    {% endif %}
                </div> 
                <div class="mt-4 place-self-center lg:col-span-7">
                    <p class="max-w-2xl font-light text-gray-500 md:text-lg lg:text-xl dark:text-gray-400">{{ page.shortDescription }}</p> 
                </div>                        
            </div>
        </section>
        
        <div class="mx-auto max-w-screen-xl mt-4 mb-8 lg:mb-16">
            <div class="prose dark:prose-invert mx-auto max-w-screen-xl">{{ page.description|markdown }}</div>
        </div>

        {{ include("page_section/render.html.twig", {'page': page}, with_context = false) }}
        {{ include("page_relation/list.html.twig", {'pageRelations': page.pageRelations}, with_context = false) }}
    </div>
</section>
{% endblock %}
