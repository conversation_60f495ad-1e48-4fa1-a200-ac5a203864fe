<section>
    <div class="py-8 px-4 mx-auto max-w-screen-xl lg:py-16 lg:px-6">
        <div class="mx-auto max-w-screen-sm text-center mb-8 lg:mb-16">
            <h2 class="mb-4 text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white">{{ 'consultant.list.title'|trans }}</h2>
            <p class="font-light text-gray-500 lg:mb-16 sm:text-xl dark:text-gray-400">{{ 'consultant.list.intro'|trans }}</p>
        </div>
        <div class="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {% for consultant in consultants %}
            <div class="bg-white rounded-lg border border-gray-200 shadow-sm dark:bg-gray-800 dark:border-gray-700">
                <a href="{{ path('app_consultant_show', {'slug': consultant.slug}) }}">
                    {% if null != consultant.image and consultant.image.filePath|length > 0 %}
                        <img class="p-4 w-full h-72 object-cover object-top rounded-lg" src="{{ consultant.image.filePath|imagine_filter('portrait_medium') }}" alt="{{ consultant.name }}">
                    {% endif %}
                </a>
                <div class="px-5 pb-5">
                    <h3 class="text-xl font-bold tracking-tight text-gray-900 dark:text-white">
                        <a href="{{ path('app_consultant_show', {'slug': consultant.slug}) }}">{{ consultant.name }}</a>
                    </h3>
                    <span class="text-gray-500">{{ consultant.medicalSpecialty }}</span>
                    <p class="mt-3 mb-4 font-light text-gray-500 dark:text-gray-400 line-clamp-6">{{ consultant.introTextShort|u.truncate(250) }}</p>
                    <a href="#" class="inline-flex items-center justify-center px-5 py-3 mr-3 text-base font-medium text-center text-white rounded-lg bg-primary-950 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:focus:ring-primary-900">
                        Запиши час
                        <svg class="w-5 h-5 ml-2 -mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>