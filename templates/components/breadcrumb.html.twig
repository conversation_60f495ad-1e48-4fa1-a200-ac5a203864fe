{% if breadcrumbs is defined %}
<nav class="flex mx-auto max-w-screen-xl mt-5 px-4 hidden sm:block" aria-label="Breadcrumb">
    <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
        {% for item in breadcrumbs %}
            {% if loop.first %}
                <li class="inline-flex items-center">
                    <a href="{{ path('app_homepage') }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-primary-950 dark:text-gray-400 dark:hover:text-white">
                        <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                        </svg>
                        {{ 'app.home'|trans }}
                    </a>
                </li>
            {% endif %}
            <li>
                <div class="flex items-center">
                    <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                    </svg>
                    {% if item.href is defined %}
                        <a href="{{ item.href }}" class="ms-1 text-sm font-medium text-gray-700 hover:text-primary-950 md:ms-2 dark:text-gray-400 dark:hover:text-white">{{ item.name }}</a>
                    {% else %}
                        <span class="text-sm text-gray-400">{{ item.name }}</span>
                    {% endif %}
                </div>
            </li>
        {% endfor %}
    </ol>
</nav>
{% endif %}
