<header>
  <nav class="bg-primary-800 border-secondary-200 px-4 lg:px-6 py-2.5 dark:bg-gray-950">
    <div class="flex flex-wrap justify-center sm:justify-between items-center mx-auto max-w-screen-xl">
      <a href="{{ path('app_homepage') }}" class="flex items-center mb-5 sm:mb-0" aria-label="{{ 'app.title'|trans }}">
        <img width="220" height="81" class="sm:flex" src="{{ asset('images/logo_horizontal_white.svg') }}" alt="">  
      </a>
      <div class="flex items-center lg:order-2">
        <a href="{{ path('app_homepage', {'_locale': app.locale == 'en' ? 'bg' : 'en'}) }}" class="text-white dark:text-white hover:bg-gray-50 hover:text-gray-700 focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-4 lg:px-5 py-2 lg:py-2.5 mr-2 dark:hover:bg-gray-700 focus:outline-none dark:focus:ring-gray-800">
          {{ app.locale == 'en' ? 'BG' : 'EN' }}
        </a>
        <button
                {{ stimulus_controller('theme-toggle', {darkLabel: 'Dark mode', lightLabel: 'Light mode'}) }}
                {{ stimulus_action('theme-toggle', 'toggle') }}
                type="button" title="Switch Mode" class="text-white dark:text-gray-400 hover:bg-gray-100 hover:text-gray-700 dark:hover:bg-gray-700 dark:hover:text-white focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5">
          <svg {{ stimulus_target('theme-toggle', 'darkIcon') }} class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path></svg>
          <svg {{ stimulus_target('theme-toggle', 'lightIcon') }} class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" fill-rule="evenodd" clip-rule="evenodd"></path></svg>
          <span {{ stimulus_target('theme-toggle', 'label') }} class="sr-only">Dark Mode</span>
        </button>

        {#
        {{ include('components/nav_user_menu.html.twig') }}
        #}

        <a href="#" class="text-white bg-primary-950 hover:bg-primary-600 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-4 lg:px-5 py-2 lg:py-2.5 mr-2 ml-2 dark:bg-primary-800 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">{{ 'button.calculator'|trans }}</a>
                

        <button data-collapse-toggle="mobile-menu-2" type="button" class="inline-flex items-center p-2 ml-1 text-sm text-white rounded-lg lg:hidden hover:bg-secondary-100 focus:outline-none focus:ring-2 focus:ring-secondary-200 dark:text-secondary-400 dark:hover:bg-secondary-700 dark:focus:ring-secondary-600" aria-controls="mobile-menu-2" aria-expanded="false">
          <span class="sr-only">Open main menu</span>
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path></svg>
          <svg class="hidden w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
        </button>
      </div>
      <div class="hidden justify-between items-center w-full lg:flex lg:w-auto lg:order-1" id="mobile-menu-2">
        {% set categories = category__findByTag(constant('App\\Entity\\Category::TAG_TOP')) %}
        {% for category in categories %}
          {% if loop.first %}
          <ul class="flex flex-col mt-4 font-medium lg:flex-row lg:space-x-8 lg:mt-0">
          {% endif %}

          <li>
            <a href="{{ category__getUrl(category) }}" class="{{ category__getUrl(category) == app.request.pathinfo ? 'text-grey-500 dark:text-primary-500' : 'text-white dark:text-white' }} block py-2 pr-4 pl-3 border-b border-secondary-100 hover:text-black lg:border-0 lg:p-0 dark:hover:text-primary-500 dark:border-secondary-700">{{ category.name }}</a>
          </li>

          {% if loop.last %}
            </ul>
          {% endif %}
        {% endfor %}
      </div>
    </div>
  </nav>
</header>