{% for label, messages in app.flashes %}
    {% if loop.first %}<div class="container mt-2">{% endif %}
    {% for message in messages %}
        <div class="alert alert-{{ label }}">
            {{ message|trans }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    {% endfor %}
    {% if loop.last %}</div>{% endif %}
{% endfor %}