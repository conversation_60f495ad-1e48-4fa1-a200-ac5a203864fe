{% if is_granted('ROLE_USER') %}
<button type="button" class="flex mx-3 text-sm bg-primary-800 rounded-full focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600" id="user-menu-button" aria-expanded="false" data-dropdown-toggle="dropdown_user_menu">
    <span class="sr-only">Open user menu</span>
    {{ ux_icon('basil:user-solid', {class: 'w-8 h-8 rounded-full fill-none text-white'}) }}
</button>
<!-- Dropdown menu -->
<div class="hidden z-50 my-4 w-56 text-base list-none bg-white rounded divide-y divide-gray-100 shadow dark:bg-gray-700 dark:divide-gray-600" id="dropdown_user_menu">
    <div class="py-3 px-4">
        <span class="block text-sm font-semibold text-gray-900 dark:text-white">{{ app.user.name }}s</span>
        <span class="block text-sm font-light text-gray-500 truncate dark:text-gray-400">{{ app.user.email }}</span>
    </div>
    <ul class="py-1 font-light text-gray-500 dark:text-gray-400" aria-labelledby="dropdown">
        <li>
            <a href="#" class="block py-2 px-4 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-400 dark:hover:text-white">My profile</a>
        </li>
        {% if is_granted('ROLE_ADMIN') %}
            <li>
                <a href="{{ path('app_admin') }}" target="_blank" class="block py-2 px-4 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-400 dark:hover:text-white">Admin panel</a>
            </li>
        {% endif %}
    </ul>
    <ul class="py-1 font-light text-gray-500 dark:text-gray-400" aria-labelledby="dropdown">
        <li>
            <a href="{{ path('app_logout') }}" class="block py-2 px-4 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">{{ 'button.logout'|trans }}</a>
        </li>
    </ul>
</div>
{% else %}
    <a href="{{ path('app_login') }}" class="text-secondary-800 dark:text-white hover:bg-secondary-50 focus:ring-4 focus:ring-secondary-300 font-medium rounded-lg text-sm px-4 py-2 lg:px-5 lg:py-2.5 mr-2 dark:hover:bg-secondary-700 focus:outline-none dark:focus:ring-secondary-800">{{ 'button.login'|trans }}</a>
{% endif %}
