<footer class="bg-gray-50 dark:bg-gray-800 mt-16">
    <div class="p-4 py-6 mx-auto max-w-screen-xl md:p-8 lg:-10">
        <div class="grid grid-cols-2 gap-8 lg:grid-cols-5">
            <div class="col-span-2">
                <a href="{{ path('app_homepage') }}" class="flex items-center mb-2 text-2xl font-semibold text-gray-900 sm:mb-0 dark:text-white" aria-label="{{ 'app.title'|trans }}">
                    <img width="181" height="63" class="sm:flex dark:hidden" src="{{ asset('images/logo_'~ app.locale ~'.svg') }}" alt="{{ 'app.title'|trans }}">
                    <img width="181" height="63" class="hidden dark:block" src="{{ asset('images/logo_dark_'~ app.locale ~'.svg') }}" alt="{{ 'app.title'|trans }}">
                </a>
                <p class="my-4 font-light text-gray-500 dark:text-gray-400">{{ 'footer.text'|trans }}</p>
            </div>
            <div class="lg:mx-auto">
                <h2 class="mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-white">{{ 'app.company'|trans }}</h2>
                {% set categories = category__findByTag(constant('App\\Entity\\Category::TAG_FOOTER_1')) %}
                {% for category in categories %}
                    {% if loop.first %}
                        <ul class="text-gray-500 dark:text-gray-400">
                    {% endif %}
                    <li class="mb-4">
                        <a href="{{ category__getUrl(category) }}" class="hover:underline">{{ category.name }}</a>
                    </li>
                    {% if loop.last %}
                        </ul>
                    {% endif %}
                {% endfor %}
            </div>
            <div class="lg:mx-auto">
                <h2 class="mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-white">{{ 'footer.follow_us'|trans }}</h2>
                {% set categories = category__findByTag(constant('App\\Entity\\Category::TAG_SOCIAL')) %}
                {% for category in categories %}
                    {% if loop.first %}
                        <ul class="text-gray-500 dark:text-gray-400">
                    {% endif %}
                    <li class="mb-4 flex flex-row items-center gap-1">
                        {% if category.icon|length > 0 %}
                            {{ ux_icon(category.icon) }}
                        {% endif %}
                        <a href="{{ category__getUrl(category) }}" class="hover:underline">{{ category.name }}</a>
                    </li>
                    {% if loop.last %}
                        </ul>
                    {% endif %}
                {% endfor %}
            </div>
            <div class="lg:mx-auto">
                <h2 class="mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-white">{{ 'footer.legal'|trans }}</h2>
                {% set categories = category__findByTag(constant('App\\Entity\\Category::TAG_FOOTER_2')) %}
                {% for category in categories %}
                    {% if loop.first %}
                        <ul class="text-gray-500 dark:text-gray-400">
                    {% endif %}
                    <li class="mb-4">
                        <a href="{{ category__getUrl(category) }}" class="hover:underline">{{ category.name }}</a>
                    </li>
                    {% if loop.last %}
                        </ul>
                    {% endif %}
                {% endfor %}
            </div>
        </div>
        <hr class="my-6 border-gray-200 sm:mx-auto dark:border-gray-700 lg:my-8">
        <span class="block text-sm text-center text-gray-500 dark:text-gray-400">© 2024 <a href="{{ path('app_homepage') }}" class="hover:underline">{{ 'app.company'|trans }}™</a>. {{ 'app.copyright'|trans }}</span>
    </div>
</footer>