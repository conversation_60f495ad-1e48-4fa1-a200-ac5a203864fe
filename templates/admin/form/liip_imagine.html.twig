{# @var field \EasyCorp\Bundle\EasyAdminBundle\Dto\FieldDto #}
{# @var entity \EasyCorp\Bundle\EasyAdminBundle\Dto\EntityDto #}

{% block liip_imagine_row %}
    {% set field = form.vars.ea_crud_form.ea_field %}
    {% set entity = form.vars.ea_crud_form.ea_entity %}
    <div class="{{ field.columns }}">
        <div class="form-group">
            {% if field.value|length > 0 %}
                <img class="rounded" src="{{ field.value|imagine_filter(field.customOptions.get('filter')) }}" alt="">

                {% set downloadRoute = field.customOptions.get(constant('App\\EasyAdmin\\Field\\LiipImagineField::OPTION_DOWNLOAD_ROUTE')) %}
                {% if downloadRoute|length > 0 %}
                    <a href="{{ path(downloadRoute, {'id': entity.instance.id}) }}" class="btn btn-sm" title="Download">
                        <i class="fa fa-download"></i>
                    </a>
                {% endif %}
            {% endif %}

            <div class="form-widget">
                {{ form_widget(form) }}
            </div>
        </div>
    </div>
{% endblock %}