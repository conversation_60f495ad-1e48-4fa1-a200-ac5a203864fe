{% block file_widget %}
    {% set field = ea_crud_form.ea_field %}
    {% if field.customOptions.get('path_field')|length > 0 %}
        {% set entity = form.parent.vars.data %}
        {% if null != entity %}
            <div class="mb-3">
                {% set filePath = attribute(entity, field.customOptions.get('path_field')) %}
                {% set fileMimeType = attribute(entity, field.customOptions.get('mime_field')) %}
                {% if fileMimeType starts with 'image' %}
                    <img class="rounded" src="{{ filePath|imagine_filter('thumbnail_sm') }}" alt="">
                {% else %}
                    {{ filePath|u.truncate(20) }}
                {% endif %}

                {% set downloadRoute = field.customOptions.get(constant('App\\EasyAdmin\\Field\\FileUploaderField::OPTION_DOWNLOAD_ROUTE')) %}
                {% if downloadRoute|length > 0 and filePath|length > 0 %}
                    <a href="{{ path(downloadRoute, {'id': entity.id}) }}" class="btn btn-sm mx-2" title="Download">
                        <i class="fa fa-download"></i>
                    </a>
                {% endif %}

                {% set deleteRoute = field.customOptions.get(constant('App\\EasyAdmin\\Field\\FileUploaderField::OPTION_DELETE_ROUTE')) %}
                {% if deleteRoute|length > 0 and filePath|length > 0 %}
                    <a href="{{ path(deleteRoute, {'id': entity.id, 'url': app.request.uri}) }}" class="btn btn-sm btn-danger mx-2" title="Delete">
                        <i class="fa fa-trash"></i>
                    </a>
                {% endif %}
            </div>
        {% endif %}
    {% endif %}

    {{ form_widget(form) }}
{% endblock %}