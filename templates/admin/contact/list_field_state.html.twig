<span class="badge
        {% if constant('App\\Entity\\Contact::STATE_START') == entity.instance.state %}
            badge-light
        {% elseif constant('App\\Entity\\Contact::STATE_WAIT_FOR_VERIFIER') == entity.instance.state %}
            badge-warning
        {% elseif constant('App\\Entity\\Contact::STATE_COMPLETED') == entity.instance.state %}
            badge-success
        {% elseif constant('App\\Entity\\Contact::STATE_CANCELED') == entity.instance.state %}
            badge-danger
        {% else %}
            badge-default
        {% endif %}
        ">{{ entity.instance.state|enum_label('App\\Enum\\ContactStateEnum')}}</span>

