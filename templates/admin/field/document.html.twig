{# @var ea \EasyCorp\Bundle\EasyAdminBundle\Context\AdminContext #}
{# @var field \EasyCorp\Bundle\EasyAdminBundle\Dto\FieldDto #}
{# @var entity \EasyCorp\Bundle\EasyAdminBundle\Dto\EntityDto #}
{% if field.value.filePath|length > 0 %}
    {% if field.value.fileMimeType starts with 'image' %}
        <img class="rounded" src="{{ field.value.filePath|imagine_filter('portrait_small') }}" alt="">
    {% else %}
        {{ field.value.originalFilename|u.truncate(20) }}
    {% endif %}
{% endif %}