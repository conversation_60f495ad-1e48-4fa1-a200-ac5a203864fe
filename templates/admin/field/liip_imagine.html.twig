{# @var ea \EasyCorp\Bundle\EasyAdminBundle\Context\AdminContext #}
{# @var field \EasyCorp\Bundle\EasyAdminBundle\Dto\FieldDto #}
{# @var entity \EasyCorp\Bundle\EasyAdminBundle\Dto\EntityDto #}

{% if field.value|length > 0 %}
    <img class="rounded" src="{{ field.value|imagine_filter(field.customOptions.get('filter')) }}" alt="">

    {% set downloadRoute = field.customOptions.get(constant('App\\EasyAdmin\\Field\\FileUploaderField::OPTION_DOWNLOAD_ROUTE')) %}
    {% if downloadRoute|length > 0 %}
        <a href="{{ path(downloadRoute, {'id': entity.instance.id}) }}" class="btn btn-sm" title="Download">
            <i class="fa fa-download"></i>
        </a>
    {% endif %}
{% endif %}