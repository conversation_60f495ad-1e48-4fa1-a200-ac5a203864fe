{# @var ea \EasyCorp\Bundle\EasyAdminBundle\Context\AdminContext #}
{# @var field \EasyCorp\Bundle\EasyAdminBundle\Dto\FieldDto #}
{# @var entity \EasyCorp\Bundle\EasyAdminBundle\Dto\EntityDto #}
<span class="{% if null != field.customOptions.get('backgroundColor') or null != field.customOptions.get('textColor') %}badge{% endif %}
        {% if null != field.customOptions.get('backgroundColor') %}bg-{{ field.customOptions.get('backgroundColor') }}{% endif %}
        {% if null != field.customOptions.get('textColor') %}text-{{ field.customOptions.get('textColor') }}{% endif %}">
{{ field.value|enum_label(field.customOptions.get('enum')) }}
</span>