{# @var ea \EasyCorp\Bundle\EasyAdminBundle\Context\AdminContext #}
{# @var field \EasyCorp\Bundle\EasyAdminBundle\Dto\FieldDto #}
{# @var entity \EasyCorp\Bundle\EasyAdminBundle\Dto\EntityDto #}

{% if field.customOptions.get('path_field')|length > 0 %}
    {% set filePath = attribute(entity.instance, field.customOptions.get('path_field')) %}
    {% set fileMimeType = attribute(entity.instance, field.customOptions.get('mime_field')) %}

    {% if fileMimeType starts with 'image' %}
        <img class="rounded" src="{{ filePath|imagine_filter('thumbnail_sm') }}" alt="">
    {% else %}
        {{ filePath|u.truncate(20) }}
    {% endif %}

    {% set downloadRoute = field.customOptions.get(constant('App\\EasyAdmin\\Field\\FileUploaderField::OPTION_DOWNLOAD_ROUTE')) %}
    {% if downloadRoute|length > 0 and filePath|length > 0 %}
        <a href="{{ path(downloadRoute, {'id': entity.instance.id}) }}" class="btn btn-sm" title="Download">
            <i class="fa fa-download"></i>
        </a>
    {% endif %}
{% endif %}