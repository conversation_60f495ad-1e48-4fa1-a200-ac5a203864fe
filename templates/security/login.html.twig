{% extends 'base.html.twig' %}

{% block title %}{{ 'button.login'|trans }}{% endblock %}

{% block breadcrumbs %}
    {% set breadcrumbs = [] %}
    {% set breadcrumbs = breadcrumbs|merge([{'name':'button.login'|trans }]) %}
    {{ parent() }}
{% endblock %}

{% block body %}

    <div class="px-4 mx-auto max-w-screen-sm text-center lg:px-6 mb-2 lg:mb-6">
        <h2 class="text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white">{{ 'button.login'|trans }}</h2>
    </div>

    <div class="mx-auto max-w-screen-xl">
        {{ form_start(form, {'attr': {class: 'grid grid-cols-1 gap-4 mx-auto max-w-screen-md'}}) }}
        {{ form_rest(form) }}
        <button type="submit" class="py-3 px-5 text-sm font-medium text-center text-white rounded-lg bg-primary-700 sm:w-fit hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">{{ 'login.form.submit'|trans }}</button>
        {{ form_end(form) }}
    </div>
{% endblock %}
