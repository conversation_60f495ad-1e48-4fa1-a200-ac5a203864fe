<ul class="list-group list-group-flush">
    {% for transition in workflow_transitions(object) %}
        {% set transitionLabel = object.workflowName ~ '.' ~ 'transition' ~ '.' ~ transition.name %}
        <li class="list-group-item">
            <a href="#workflowApplyTransition" data-toggle="modal" data-target="#workflowApplyTransition" data-remote="{{ path('app_workflow_apply_transition_with_form', {'entity':object.workflowName, 'id':object.id, 'transition':transition.name, 'targetUrl':targetUrl}) }}" data-transition="{{ transitionLabel|trans }}">{{ transitionLabel|trans }}</a>
        </li>
    {% else %}
        <li class="list-group-item">{{ 'label.no_info'|trans }}</li>
    {% endfor %}
</ul>

<div class="modal fade bsModalRemote" id="workflowApplyTransition" tabindex="-1" role="dialog" aria-labelledby="workflowApplyTransitionLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="workflowApplyTransitionLabel">New transition</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- remote content load here -->
            </div>
        </div>
    </div>
</div>
