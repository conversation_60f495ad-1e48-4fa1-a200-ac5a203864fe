<!-- Button trigger workflow modal -->
<button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#workflowModal">
    {{ 'label.workflow_diagram'|trans }}
</button>
<!-- Workflow Modal -->
<div class="modal fade" id="workflowModal" tabindex="-1" role="dialog" aria-labelledby="workflowModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="workflowModalLabel">{{ 'label.workflow_diagram'|trans }}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                {% include 'workflow/svg_viewer.html.twig' with {'object': object, 'transitions': workflow__getTransitions(object), 'places': workflow__getPlaces(object) } %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
