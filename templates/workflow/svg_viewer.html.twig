{{ include('workflow/state_machine.'~ workflow_metadata(object, 'entity') ~'.svg.twig') }}

<script>
    var findArr = [];
    {% for place in places %}
        {% set placeLabel = object.workflowName ~ '.' ~ 'place' ~ '.' ~ place %}
        findArr['{{ place }}'] = '{{ placeLabel|trans }}';
    {% endfor %}
    {% for transition in transitions %}
        {% set transitionLabel = object.workflowName ~ '.' ~ 'transition' ~ '.' ~ transition.name %}
        findArr['{{ transition.name }}'] = '{{ transitionLabel|trans }}';
    {% endfor %}

    document.addEventListener('DOMContentLoaded', function (event) {
        {% if workflow_marked_places(object)|length > 0 %}
        var name = "{{ workflow_marked_places(object)|join|sha1_encode }}";
        var svg = document.querySelector('#state_machine-{{ workflow_metadata(object, 'entity') }}');

        svg.querySelectorAll('title').forEach((element) => {
            if (element.innerHTML === 'place_' + name) {
                var ref = element.parentElement.querySelector('ellipse');
                ref.setAttribute('stroke', 'red');
                var newEllipse = ref.cloneNode(true);
                newEllipse.setAttribute('rx', ref.getAttribute('rx') * .9);
                newEllipse.setAttribute('ry', ref.getAttribute('ry') * .9);
                ref.after(newEllipse);
            }
        });

        svg.querySelectorAll('text').forEach((element) => {
            element.innerHTML = findArr[element.innerHTML];
        });

        {% endif %}
    });
</script>
