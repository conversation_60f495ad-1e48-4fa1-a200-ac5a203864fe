{% for workflowLog in workflowLogs %}
    {% if loop.first %}
        <table class="table">
        <thead>
        <tr>
            <th scope="col">{{ 'workflowLog.label.from'|trans }}</th>
            <th scope="col">{{ 'workflowLog.label.transition'|trans }}</th>
            <th scope="col">{{ 'workflowLog.label.to'|trans }}</th>
            <th scope="col">{{ 'Created By'|trans }}</th>
            <th scope="col">{{ 'Created At'|trans }}</th>
            <th scope="col">{{ 'Attributes'|trans }}</th>
        </tr>
        </thead>
        <tbody>
    {% endif %}
    <tr>
        <td scope="row">
            {% set placeLabel = workflowLog.subject.workflowName ~ '.' ~ 'place' ~ '.' ~ workflowLog.froms %}
            {{ placeLabel|trans }}
        </td>
        <td>
            {% set transitionLabel = workflowLog.subject.workflowName ~ '.' ~ 'transition' ~ '.' ~ workflowLog.transition %}
            {{ transitionLabel|trans }}
        </td>
        <td>
            {% set placeLabel = workflowLog.subject.workflowName ~ '.' ~ 'place' ~ '.' ~ workflowLog.tos %}
            {{ placeLabel|trans }}
        </td>
        <td>{{ workflowLog.createdBy ? workflowLog.createdBy : 'workflowLog.label.system_account'|trans  }}</td>
        <td>{{ workflowLog.createdAt|format_datetime }}</td>
        <td>{{ workflowLog.attributes|join }}</td>
    </tr>
    {% if loop.last %}
        </tbody>
        </table>
    {% endif %}
{% else %}
    <p>{{ 'workflowLog.label.no_track_changes'|trans }}</p>
{% endfor %}

