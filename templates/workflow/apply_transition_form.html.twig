{{ form_start(form, {'action': path('app_workflow_apply_transition_with_form', {'entity':entity, 'id':id, 'transition':transition, 'workflowName':workflowName, 'targetUrl':targetUrl}), 'attr': {'onsubmit': 'formDisabledButtonAndSubmit(event)'} }) }}
{{ form_end(form) }}

<script>
    function formDisabledButtonAndSubmit(e) {
        e.preventDefault();
        var form = e.target;
        form.querySelector("button").setAttribute("disabled", "disabled");
        form.querySelector("button[type='submit']").innerHTML = '<i class="fa fa-spinner fa-spin"></i> Please wait...';
        form.submit();
    }
</script>




