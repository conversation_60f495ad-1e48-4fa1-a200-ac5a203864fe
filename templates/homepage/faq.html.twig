{% set pages = page__findByTag(constant('App\\Entity\\Page::TAG_HOMEPAGE_FAQ'), 1) %}
{% if pages|length > 0 %}
    {% set page = pages|first %}
    {# @var page \App\Entity\Page #}
    <section class="bg-white dark:bg-gray-900">
        <div class="py-8 px-4 mx-auto max-w-screen-xl sm:py-16 lg:px-6 ">
            <h2 class="mb-6 lg:mb-8 text-3xl lg:text-4xl tracking-tight font-extrabold text-center text-gray-900 dark:text-white">{{ page.name }}</h2>
            <p class="font-light text-gray-500 sm:text-lg md:px-20 lg:px-38 xl:px-48 dark:text-gray-400">{{ page.shortDescription }}</p>
            <div class="mx-auto max-w-screen-md">
                {{ include('page_section/types/faq.html.twig', {'sections': page.sections}, with_context = false) }}
            </div>
        </div>
    </section>
{% endif %}