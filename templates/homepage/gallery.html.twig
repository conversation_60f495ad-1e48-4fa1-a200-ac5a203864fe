{% set pages = page__findByTag(constant('App\\Entity\\Page::TAG_HOMEPAGE_GALLERY'), 1) %}
{% if pages|length > 0 %}
    {% set page = pages|first %}
    {# @var page \App\Entity\Page #}

    <section class="bg-white dark:bg-gray-900">
        <div class="py-8 px-4 mx-auto max-w-screen-xl lg:px-12 sm:text-center lg:py-16">
            <h2 class="mb-4 text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white">{{ page.name }}</h2>
            <p class="font-light mb-4 text-gray-500 sm:text-lg md:px-20 lg:px-38 xl:px-48 dark:text-gray-400">{{ page.shortDescription }}</p>
            {{ include('page_section/types/gallery.html.twig', {'sections': page.sections}, with_context = false) }}
        </div>
    </section>
{% endif %}