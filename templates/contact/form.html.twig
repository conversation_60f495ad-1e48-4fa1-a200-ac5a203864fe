{{ form_start(form, { method: 'POST', action: path('app_contact'), 'attr': {class: 'grid grid-cols-1 gap-4 mx-auto max-w-screen-md sm:grid-cols-2'} }) }}
{{ form_row(form.subject) }}
{{ form_row(form.name) }}
{{ form_row(form.email) }}
{{ form_row(form.phone) }}
<div class="sm:col-span-2">
{{ form_row(form.message) }}
</div>

<div class="sm:col-span-2">
    {{ form_row(form.sendToMe) }}
    {{ form_row(form.agreeTerms, { 'label': 'contact.form.agreeTerms'|trans({
        '%url%': path('app_page_info', {'slug': 'privacy-policy', 'id': 6}),
    }) }) }}
</div>
{{ form_rest(form) }}
<button type="submit" class="py-3 px-5 text-sm font-medium text-center text-white rounded-lg bg-primary-700 sm:w-fit hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">{{ 'button.submit'|trans }}</button>
{{ form_end(form) }}
