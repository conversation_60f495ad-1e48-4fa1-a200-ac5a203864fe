{% extends 'base.html.twig' %}

{% block title %}{{ 'contact.title'|trans }}{% endblock %}
{% block meta_description %}{{ 'contact.text'|trans }}{% endblock %}

{% block breadcrumbs %}
    {% set breadcrumbs = [] %}
    {% set breadcrumbs = breadcrumbs|merge([{'name':'contact.title'|trans }]) %}
    {{ parent() }}
{% endblock %}

{% block body %}
    <section class="bg-white dark:bg-gray-900">
        <div class="max-w-screen-xl px-4 py-8 mx-auto lg:px-6 sm:py-16 lg:py-24">
            <div class="px-4 mx-auto max-w-screen-sm text-center lg:px-6 mb-8 lg:mb-16">
                <h2 class="mb-4 text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white">{{ 'contact.title'|trans }}</h2>
                <p class="font-light text-gray-600 dark:text-gray-400 sm:text-xl">{{ 'contact.text'|trans }}</p>
            </div>

            <div class="grid grid-cols-1 gap-6 text-center sm:gap-16 sm:grid-cols-2 lg:grid-cols-3">
                <div>
                    <div
                            class="inline-flex items-center justify-center w-16 h-16 mx-auto text-gray-500 bg-gray-100 rounded-lg dark:bg-gray-800 dark:text-white">
                        <svg aria-hidden="true" class="w-10 h-10" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                             fill="currentColor">
                            <path fill-rule="evenodd"
                                  d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z"
                                  clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="mt-4">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">
                            {{ 'contact.company_information.title'|trans|nl2br }}
                        </h3>
                        <p class="mt-1 text-base font-normal text-gray-500 dark:text-gray-400">
                            {{ 'contact.company_information.vat'|trans }}<br>
                            {{ 'contact.company_information.iban'|trans }}
                        </p>
                    </div>
                </div>

                <div>
                    <div
                            class="inline-flex items-center justify-center w-16 h-16 mx-auto text-gray-500 bg-gray-100 rounded-lg dark:bg-gray-800 dark:text-white">
                        <svg aria-hidden="true" class="w-10 h-10" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                             fill="currentColor">
                            <path fill-rule="evenodd"
                                  d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                  clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="mt-4">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">
                            {{ 'contact.address.title'|trans }}
                        </h3>
                        <p class="mt-1 text-base font-normal text-gray-500 dark:text-gray-400">
                            {{ 'contact.address.text'|trans|nl2br }}
                        </p>
                    </div>
                </div>

                <div>
                    <div
                            class="inline-flex items-center justify-center w-16 h-16 mx-auto text-gray-500 bg-gray-100 rounded-lg dark:bg-gray-800 dark:text-white">
                        <svg aria-hidden="true" class="w-10 h-10" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                             fill="currentColor">
                            <path
                                    d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                        </svg>
                    </div>
                    <div class="mt-4">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">
                            {{ 'contact.contact_us.title'|trans }}
                        </h3>
                        <p class="mt-1 text-base font-normal text-gray-500 dark:text-gray-400">
                            {{ 'contact.contact_us.text'|trans|nl2br }}
                        </p>
                        <a href="#" title="" class="block mt-1 text-base font-semibold text-gray-900 dark:text-white hover:underline">
                            {{ 'contact.phone'|trans }}
                        </a>
                    </div>
                </div>
            </div>

            <div class="max-w-3xl mx-auto mt-8 lg:mt-24">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
                    {{ 'contact.contact_form.title'|trans }}
                </h2>
                <p class="mb-3 font-light text-gray-600 dark:text-gray-400 sm:text-xl">{{ 'contact.contact_form.text'|trans }}</p>
                {% include 'contact/form.html.twig' with {form: form} only %}
            </div>
        </div>
    </section>

    {% set googleMapUrl = 'https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d5865.330911294345!2d23.317328918999404!3d42.68962839999998!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x40aa850d02bd61a9%3A0xa146c2abd2ef95db!2sSofia%20Center%2C%20ul.%20%22Han%20Asparuh%22%2054%2C%201000%20Sofia!5e0!3m2!1sen!2sbg!4v1728373576393!5m2!1sen!2sbg' %}
    {% if 'bg' == app.locale %}
        {% set googleMapUrl = 'https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d5865.330911294345!2d23.317328918999404!3d42.68962839999998!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x40aa850d02bd61a9%3A0xa146c2abd2ef95db!2sSofia%20Center%2C%20ul.%20%22Han%20Asparuh%22%2054%2C%201000%20Sofia!5e0!3m2!1sbg!2sen!4v1728373576393!5m2!1sbg!2sen' %}
    {% endif %}

    <iframe src="{{ googleMapUrl }}" width="100%" height="450" style="border:0; height: 450px; width: 100%; padding: 0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
{% endblock %}
