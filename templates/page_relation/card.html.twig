{# @var pageRelation \App\Entity\PageRelation #}
{# @var page \App\Entity\Page #}
{% set page = pageRelation.relatedPage %}
<article class="p-4 bg-white rounded-lg border border-gray-200 shadow-md dark:bg-gray-800 dark:border-gray-700">
    <a href="{{ path('app_page_info', {'id':page.id, 'slug':page.slug}) }}">
        {% if page.icon|length > 0 %}
            <div class="flex justify-center items-center mb-4 w-20 h-20 rounded bg-primary-100 lg:h-20 lg:w-20 dark:bg-primary-900">
                {{ ux_icon(page.icon, {class: 'w-14 h-14 fill-none text-primary-950 dark:text-white'}) }}
            </div>
        {% elseif page.image|length > 0 %}
            <img class="mb-5 rounded-lg" src="{{ page.image|imagine_filter('card_image_thumbnail') }}" alt="{{ page.name }}">
        {% endif %}

        {% for category in page.categories %}
            <span class="bg-purple-100 text-purple-800 text-xs font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-purple-200 dark:text-purple-900">{{ category }}</span>
        {% endfor %}
        <h2 class="my-2 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">{{ pageRelation.name }}</h2>
        <p class="mb-4 font-light text-gray-500 dark:text-gray-400">{{ pageRelation.shortDescription }}</p>
    </a>
</article>
