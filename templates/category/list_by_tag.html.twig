{% extends 'base.html.twig' %}

{% block title %}{{ tag|trans }}!{% endblock %}
{% block meta_description %}{{ 'label.search_by'|trans }}: {{ tag|trans }}{% endblock %}

{% block breadcrumbs %}
    {% set breadcrumbs = [] %}
    {% set breadcrumbs = breadcrumbs|merge([{'name': tag|trans|u.truncate(50, '...', false) }]) %}
    {{ parent() }}
{% endblock %}

{% block body %}
    <section class="bg-white dark:bg-gray-900">
        <div class="py-8 px-4 mx-auto max-w-screen-xl lg:py-16 lg:px-6">
            <div class="mx-auto max-w-screen-lg text-center mb-8 lg:mb-16">
                <h2 class="mb-4 text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white">{{ tag|trans }}</h2>
                <p class="font-light text-gray-500 sm:text-xl dark:text-gray-400">{{ 'label.search_by'|trans }}: {{ tag|trans }}</p>
            </div>
            <div class="grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
                {% for page in pages.currentPageResults %}
                    {{ include("page/card.html.twig", {'page': page}, with_context = false) }}
                {% else %}
                    <p>{{ 'label.no_info'|trans }}</p>
                {% endfor %}
            </div>
        </div>

        {% if pages.haveToPaginate %}
            <div class="flex justify-center">
                {{ pagerfanta(pages, null, { routeName: 'app_page_list_by_tag', 'routeParams': {'tag': tag} }) }}
            </div>
        {% endif %}
    </section>
{% endblock %}

