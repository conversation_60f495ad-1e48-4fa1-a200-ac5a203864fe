{% extends 'base.html.twig' %}

{% block title %}{{ category.name }}!{% endblock %}
{% block meta_description %}{{ category.shortDescription }}{% endblock %}

{% block breadcrumbs %}
    {% set breadcrumbs = [] %}
    {% set breadcrumbs = breadcrumbs|merge([{'name': category.name|u.truncate(50, '...', false) }]) %}
    {{ parent() }}
{% endblock %}

{% block body %}
    <section class="bg-white dark:bg-gray-900">
        <div class="py-8 px-4 mx-auto max-w-screen-xl lg:py-16 lg:px-6">
            <div class="mx-auto max-w-screen-lg text-center mb-8 lg:mb-16">
                <h2 class="mb-4 text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white">{{ category.name }}</h2>
                {% if category.description|length > 0 %}
                    <p class="font-light text-gray-500 sm:text-xl dark:text-gray-400">{{ category.description|nl2br }}</p>
                {% endif %}
            </div>
            {% for childCategory in category__getChildren(category) %}
                {% if loop.first %}<div class="grid gap-8 sm:grid-cols-2 lg:grid-cols-3 mb-20">{% endif %}
                {{ include("category/card.html.twig", {'category': childCategory}, with_context = false) }}
                {% if loop.last %}</div>{% endif %}
            {% endfor %}

            {% for page in pages.currentPageResults %}
                {% if loop.first %}<div class="grid gap-8 sm:grid-cols-2 lg:grid-cols-3">{% endif %}
                {{ include("page/card.html.twig", {'page': page, 'show_date': category.template == constant('App\\Entity\\Category::TEMPLATE_NEWS')}, with_context = false) }}
                {% if loop.last %}</div>{% endif %}
            {% endfor %}
        </div>

        {% if pages.haveToPaginate %}
            <div class="flex justify-center">
                {{ pagerfanta(pages, null, { routeName: 'app_page_list', 'routeParams': {'slug': category.slug, 'id': category.id} }) }}
            </div>
        {% endif %}
    </section>
{% endblock %}

