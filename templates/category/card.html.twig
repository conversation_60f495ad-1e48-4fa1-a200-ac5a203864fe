{# @var category App\Entity\Category #}
<article class="p-4 bg-white rounded-lg border border-gray-200 shadow-md dark:bg-gray-800 dark:border-gray-700">
    <a href="{{ category__getUrl(category) }}">
        {% if category.icon|length > 0 %}
            <div class="flex justify-center items-center mb-4 w-20 h-20 rounded bg-primary-100 lg:h-20 lg:w-20 dark:bg-primary-900">
                {{ ux_icon(category.icon, {class: 'w-14 h-14 fill-none text-primary-950 dark:text-white'}) }}
            </div>
        {% elseif category.image|length > 0 %}
            <img class="mb-5 rounded-lg" src="{{ category.image|imagine_filter('card_image_thumbnail') }}" alt="{{ category.name }}">
        {% endif %}
        <h3 class="mb-2 text-xl font-bold dark:text-white">{{ category.name }}</h3>
        <p class="font-light text-gray-500 dark:text-gray-400">{{ category.shortDescription|u.truncate(255) }}</p>
    </a>
</article>