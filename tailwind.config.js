/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: 'selector',
  content: [
    "./vendor/tales-from-a-dev/flowbite-bundle/templates/**/*.html.twig",
    "./assets/**/*.js",
    "./templates/**/*.html.twig",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          '50': '#fff1f2',
          '100': '#ffe0e2',
          '200': '#ffc6ca',
          '300': '#ff9ea5',
          '400': '#ff6772',
          '500': '#fc3745',
          '600': '#ea1827',
          '700': '#c5101d',
          '800': '#9f111b',
          '900': '#86161e',
          '950': '#4a050a',
        },
        secondary: {
          '50': '#f5f6f6',
          '100': '#e6e7e7',
          '200': '#cfd0d2',
          '300': '#aeb0b2',
          '400': '#939598',
          '500': '#6a6c70',
          '600': '#5b5c5f',
          '700': '#4d4e51',
          '800': '#444546',
          '900': '#3b3c3e',
          '950': '#252527',
        }
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
}
