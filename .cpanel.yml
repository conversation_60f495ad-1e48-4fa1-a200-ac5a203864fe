---
deployment:
    tasks:
        - export DEPLOYPATH=/home/<USER>
        - rsync -avu public/ $DEPLOYPATH/public_html
        - rsync -avu --delete assets $DEPLOYPATH
        - rsync -avu --delete bin $DEPLOYPATH
        - rsync -avu --delete config $DEPLOYPATH
        - rsync -avu --delete src $DEPLOYPATH
        - rsync -avu --delete templates $DEPLOYPATH
        - rsync -avu --delete tests $DEPLOYPATH
        - rsync -avu --delete translations $DEPLOYPATH
        - rsync -avu --delete migrations $DEPLOYPATH
        - rsync -avu composer.json $DEPLOYPATH
        - rsync -avu tailwind.config.js $DEPLOYPATH
        - rsync -avu importmap.php $DEPLOYPATH
        - rsync -avu phpunit.xml.dist $DEPLOYPATH
