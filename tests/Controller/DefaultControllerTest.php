<?php

namespace App\Tests\Controller;

use App\Test\CustomWebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Zenstruck\Foundry\Test\ResetDatabase;

class DefaultControllerTest extends CustomWebTestCase
{
    use ResetDatabase;

    public function testIndex()
    {
        $client = static::createClient();

        $client->request(Request::METHOD_GET, '/bg/');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('h1', 'СЪВЕТ ЗА МОЗЪЧНО ЗДРАВЕ');
    }
}
