<?php

namespace App\Tests\Controller;

use App\Entity\Contact;
use App\Factory\ContactFactory;
use App\Factory\UserFactory;
use App\Test\CustomWebTestCase;
use Symfony\Component\HttpFoundation\Request;

class WorkflowControllerTest extends CustomWebTestCase
{
    public function testApplyTransitionWithForm()
    {
        $client = static::createClient();

        $contact = ContactFactory::createOne([
            'state' => Contact::TRANSITION_START,
        ]);
        $user = UserFactory::new()
            ->adminUser('<EMAIL>', '12345')
            ->create();
        $this->loginUser($client, $user->getEmail());

        $url = sprintf('/en/workflow/apply-transition-form/%s/%d/%s', 'contact', $contact->getId(), Contact::TRANSITION_COMPLETE);
        $client->request(Request::METHOD_GET, $url);
        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('label', 'Note');

        $client->submitForm('Save', [
            'workflow_transition[note]' => 'Test workflow log note',
        ]);
        $this->assertResponseRedirects();
        $client->followRedirect();
        $client->followRedirect();
        $this->assertResponseIsSuccessful();
    }
}
