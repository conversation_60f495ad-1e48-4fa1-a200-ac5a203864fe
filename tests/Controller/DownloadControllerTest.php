<?php

namespace App\Tests\Controller;

use App\Factory\CategoryFactory;
use App\Test\CustomWebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Zenstruck\Foundry\Test\ResetDatabase;

class DownloadControllerTest extends CustomWebTestCase
{
    use ResetDatabase;

    public function testDownloadImageCategory()
    {
        $client = static::createClient();

        $category = CategoryFactory::new()->withImage()->create();

        // StreamedResponse content gets sent to stdout
        ob_start();
        $client->request(Request::METHOD_GET, '/en/download/image/category/1');
        ob_get_clean();
        $this->assertResponseIsSuccessful();
    }
}
