<?php

namespace App\Tests\Controller;

use App\Factory\UserFactory;
use App\Test\CustomWebTestCase;
use Symfony\Component\HttpFoundation\Request;

class SecurityControllerTest extends CustomWebTestCase
{
    public function testLogin()
    {
        $client = static::createClient();
        $client->request(Request::METHOD_GET, '/en/login');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('label', 'Email');
    }

    public function testLoginSubmission()
    {
        $client = static::createClient();

        $email = '<EMAIL>';
        $password = 'welcome';
        UserFactory::new()->create([
            'email' => $email,
            'plainPassword' => $password,
        ]);

        $client->request(Request::METHOD_GET, '/en/login');
        $client->submitForm('Submit', [
            'email' => $email,
            'password' => $password,
        ]);
        $this->assertResponseRedirects();

        $client->followRedirect();
        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('body', $email);
    }

    public function testLogout()
    {
        $client = static::createClient();

        $email = '<EMAIL>';
        $password = 'welcome';
        UserFactory::new()->create([
            'email' => $email,
            'plainPassword' => $password,
        ]);
        $this->loginUser($client, $email);

        $client->request(Request::METHOD_GET, '/en/logout');
        $this->assertResponseRedirects('/en/');

        $client->followRedirect();
        $this->assertResponseIsSuccessful();
    }
}
