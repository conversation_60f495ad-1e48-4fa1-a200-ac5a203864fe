{"babdev/pagerfanta-bundle": {"version": "v4.4.0"}, "dama/doctrine-test-bundle": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "7.2", "ref": "896306d79d4ee143af9eadf9b09fd34a8c391b70"}, "files": ["config/packages/dama_doctrine_test_bundle.yaml"]}, "doctrine/cache": {"version": "1.10.0"}, "doctrine/collections": {"version": "1.6.4"}, "doctrine/data-fixtures": {"version": "1.4.2"}, "doctrine/dbal": {"version": "v2.10.1"}, "doctrine/deprecations": {"version": "v0.5.3"}, "doctrine/doctrine-bundle": {"version": "2.13", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.12", "ref": "7266981c201efbbe02ae53c87f8bb378e3f825ae"}, "files": ["config/packages/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-fixtures-bundle": {"version": "3.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "1f5514cfa15b947298df4d771e694e578d4c204d"}, "files": ["src/DataFixtures/AppFixtures.php"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "1d01ec03c6ecbd67c3375c5478c9a423ae5d6a33"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "doctrine/event-manager": {"version": "1.1.0"}, "doctrine/inflector": {"version": "1.3.1"}, "doctrine/instantiator": {"version": "1.3.0"}, "doctrine/lexer": {"version": "1.2.0"}, "doctrine/migrations": {"version": "2.2.1"}, "doctrine/orm": {"version": "v2.7.1"}, "doctrine/persistence": {"version": "1.3.6"}, "doctrine/reflection": {"version": "v1.1.0"}, "doctrine/sql-formatter": {"version": "1.0.1"}, "easycorp/easyadmin-bundle": {"version": "3.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "b131e6cbfe1b898a508987851963fff485986285"}, "files": []}, "egulias/email-validator": {"version": "2.1.17"}, "erusev/parsedown": {"version": "1.7.4"}, "fakerphp/faker": {"version": "v1.13.0"}, "friendsofphp/php-cs-fixer": {"version": "3.8", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "be2103eb4a20942e28a6dd87736669b757132435"}, "files": [".php-cs-fixer.dist.php"]}, "giggsey/libphonenumber-for-php": {"version": "8.12.0"}, "giggsey/locale": {"version": "1.8"}, "google/recaptcha": {"version": "1.3", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.1", "ref": "e5a4aa21f2e98d7440ae9aab6b56e307f99dd084"}}, "imagine/imagine": {"version": "1.2.3"}, "karser/karser-recaptcha3-bundle": {"version": "0.1", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "0.1", "ref": "c51ce07c10331d506762efe25b6f5843c1a5ea17"}, "files": ["config/packages/karser_recaptcha3.yaml"]}, "knplabs/knp-time-bundle": {"version": "v1.11.0"}, "league/flysystem": {"version": "1.0.66"}, "league/flysystem-bundle": {"version": "1.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "913dc3d7a5a1af0d2b044c5ac3a16e2f851d7380"}, "files": ["config/packages/flysystem.yaml", "var/storage/.gitignore"]}, "league/mime-type-detection": {"version": "1.4.0"}, "liip/imagine-bundle": {"version": "2.8", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.8", "ref": "d1227d002b70d1a1f941d91845fcd7ac7fbfc929"}, "files": ["config/packages/liip_imagine.yaml", "config/routes/liip_imagine.yaml"]}, "lorenzo/pinky": {"version": "1.0.5"}, "monolog/monolog": {"version": "2.0.2"}, "myclabs/deep-copy": {"version": "1.10.2"}, "nikic/php-parser": {"version": "v4.3.0"}, "ocramius/package-versions": {"version": "1.5.1"}, "odolbeau/phone-number-bundle": {"version": "4.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.0", "ref": "8aaba28c06547e50377dd9dce4104b9e384731a5"}, "files": ["config/packages/misd_phone_number.yaml"]}, "otobul/epaybg-bundle": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "1.0", "ref": "d1f0d9124921836288cb9d1b1147fde4d918ec58"}, "files": ["./config/packages/otobul_epaybg.yaml", "./config/routes/otobul_epaybg.yaml"]}, "phar-io/manifest": {"version": "2.0.1"}, "phar-io/version": {"version": "3.1.0"}, "php": {"version": "7.3"}, "phpdocumentor/reflection-common": {"version": "2.0.0"}, "phpdocumentor/reflection-docblock": {"version": "4.3.4"}, "phpdocumentor/type-resolver": {"version": "1.1.0"}, "phpspec/prophecy": {"version": "1.13.0"}, "phpstan/phpstan": {"version": "1.10", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "5e490cc197fb6bb1ae22e5abbc531ddc633b6767"}}, "phpunit/php-code-coverage": {"version": "9.2.6"}, "phpunit/php-file-iterator": {"version": "3.0.5"}, "phpunit/php-invoker": {"version": "3.1.1"}, "phpunit/php-text-template": {"version": "2.0.4"}, "phpunit/php-timer": {"version": "5.0.3"}, "phpunit/phpunit": {"version": "9.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "9.6", "ref": "7364a21d87e658eb363c5020c072ecfdc12e2326"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "psr/cache": {"version": "1.0.1"}, "psr/container": {"version": "1.0.0"}, "psr/event-dispatcher": {"version": "1.0.0"}, "psr/link": {"version": "1.0.0"}, "psr/log": {"version": "1.1.2"}, "sebastian/cli-parser": {"version": "1.0.1"}, "sebastian/code-unit": {"version": "1.0.8"}, "sebastian/code-unit-reverse-lookup": {"version": "2.0.3"}, "sebastian/comparator": {"version": "4.0.6"}, "sebastian/complexity": {"version": "2.0.2"}, "sebastian/diff": {"version": "4.0.4"}, "sebastian/environment": {"version": "5.1.3"}, "sebastian/exporter": {"version": "4.0.3"}, "sebastian/global-state": {"version": "5.0.2"}, "sebastian/lines-of-code": {"version": "1.0.3"}, "sebastian/object-enumerator": {"version": "4.0.4"}, "sebastian/object-reflector": {"version": "2.0.4"}, "sebastian/recursion-context": {"version": "4.0.4"}, "sebastian/type": {"version": "2.3.1"}, "sebastian/version": {"version": "3.0.2"}, "stof/doctrine-extensions-bundle": {"version": "1.10", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.2", "ref": "e805aba9eff5372e2d149a9ff56566769e22819d"}, "files": ["config/packages/stof_doctrine_extensions.yaml"]}, "symfony/asset": {"version": "v5.0.5"}, "symfony/asset-mapper": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "6c28c471640cc2c6e60812ebcb961c526ef8997f"}, "files": ["assets/app.js", "assets/styles/app.css", "config/packages/asset_mapper.yaml", "importmap.php"]}, "symfony/browser-kit": {"version": "v5.0.5"}, "symfony/cache": {"version": "v5.0.5"}, "symfony/cache-contracts": {"version": "v2.0.1"}, "symfony/config": {"version": "v5.0.5"}, "symfony/console": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "1781ff40d8a17d87cf53f8d4cf0c8346ed2bb461"}, "files": ["bin/console"]}, "symfony/css-selector": {"version": "v5.0.5"}, "symfony/debug-bundle": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "5aa8aa48234c8eb6dbdd7b3cd5d791485d2cec4b"}, "files": ["config/packages/debug.yaml"]}, "symfony/dependency-injection": {"version": "v5.0.5"}, "symfony/deprecation-contracts": {"version": "v2.1.2"}, "symfony/doctrine-bridge": {"version": "v5.0.5"}, "symfony/dom-crawler": {"version": "v5.0.5"}, "symfony/dotenv": {"version": "v5.0.5"}, "symfony/error-handler": {"version": "v5.0.5"}, "symfony/event-dispatcher": {"version": "v5.0.5"}, "symfony/event-dispatcher-contracts": {"version": "v2.0.1"}, "symfony/expression-language": {"version": "v5.0.5"}, "symfony/filesystem": {"version": "v5.0.5"}, "symfony/finder": {"version": "v5.0.5"}, "symfony/flex": {"version": "1.21", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "146251ae39e06a95be0fe3d13c807bcf3938b172"}, "files": [".env"]}, "symfony/form": {"version": "v5.0.5"}, "symfony/framework-bundle": {"version": "7.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "6356c19b9ae08e7763e4ba2d9ae63043efc75db5"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/http-client": {"version": "v5.0.5"}, "symfony/http-client-contracts": {"version": "v2.0.1"}, "symfony/http-foundation": {"version": "v5.0.5"}, "symfony/http-kernel": {"version": "v5.0.5"}, "symfony/intl": {"version": "v5.0.5"}, "symfony/mailer": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "df66ee1f226c46f01e85c29c2f7acce0596ba35a"}, "files": ["config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/messenger": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.0", "ref": "ba1ac4e919baba5644d31b57a3284d6ba12d52ee"}, "files": ["config/packages/messenger.yaml"]}, "symfony/mime": {"version": "v5.0.5"}, "symfony/monolog-bridge": {"version": "v5.0.5"}, "symfony/monolog-bundle": {"version": "3.10", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.7", "ref": "aff23899c4440dd995907613c1dd709b6f59503f"}, "files": ["config/packages/monolog.yaml"]}, "symfony/notifier": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.0", "ref": "178877daf79d2dbd62129dd03612cb1a2cb407cc"}, "files": ["config/packages/notifier.yaml"]}, "symfony/options-resolver": {"version": "v5.0.5"}, "symfony/password-hasher": {"version": "v5.3.0"}, "symfony/phpunit-bridge": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "a411a0480041243d97382cac7984f7dce7813c08"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/polyfill-intl-grapheme": {"version": "v1.14.0"}, "symfony/polyfill-intl-icu": {"version": "v1.14.0"}, "symfony/polyfill-intl-idn": {"version": "v1.14.0"}, "symfony/polyfill-intl-normalizer": {"version": "v1.14.0"}, "symfony/polyfill-mbstring": {"version": "v1.14.0"}, "symfony/polyfill-php73": {"version": "v1.14.0"}, "symfony/polyfill-php80": {"version": "v1.17.0"}, "symfony/polyfill-php81": {"version": "v1.23.0"}, "symfony/process": {"version": "v5.0.5"}, "symfony/property-access": {"version": "v5.0.5"}, "symfony/property-info": {"version": "v5.0.5"}, "symfony/routing": {"version": "7.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "21b72649d5622d8f7da329ffb5afb232a023619d"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/runtime": {"version": "v5.3.0"}, "symfony/security-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "2ae08430db28c8eb4476605894296c82a642028f"}, "files": ["config/packages/security.yaml", "config/routes/security.yaml"]}, "symfony/security-core": {"version": "v5.0.5"}, "symfony/security-csrf": {"version": "v5.0.5"}, "symfony/security-http": {"version": "v5.0.5"}, "symfony/serializer": {"version": "v5.0.5"}, "symfony/service-contracts": {"version": "v2.0.1"}, "symfony/stimulus-bundle": {"version": "2.19", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.13", "ref": "6acd9ff4f7fd5626d2962109bd4ebab351d43c43"}, "files": ["assets/bootstrap.js", "assets/controllers.json", "assets/controllers/hello_controller.js"]}, "symfony/stopwatch": {"version": "v5.0.5"}, "symfony/string": {"version": "v5.0.5"}, "symfony/translation": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "e28e27f53663cc34f0be2837aba18e3a1bef8e7b"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/translation-contracts": {"version": "v2.0.1"}, "symfony/twig-bridge": {"version": "v5.0.5"}, "symfony/twig-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "cab5fd2a13a45c266d45a7d9337e28dee6272877"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/uid": {"version": "7.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "0df5844274d871b37fc3816c57a768ffc60a43a5"}, "files": []}, "symfony/ux-icons": {"version": "2.20", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.17", "ref": "803a3bbd5893f9584969ab8670290cdfb6a0a5b5"}, "files": ["assets/icons/symfony.svg"]}, "symfony/ux-twig-component": {"version": "2.24", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.13", "ref": "9b627037a510e20eddb559e06050a34854ce51d6"}, "files": ["config/packages/twig_component.yaml"]}, "symfony/validator": {"version": "7.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "8c1c4e28d26a124b0bb273f537ca8ce443472bfd"}, "files": ["config/packages/validator.yaml"]}, "symfony/var-dumper": {"version": "v5.0.5"}, "symfony/var-exporter": {"version": "v5.0.5"}, "symfony/web-link": {"version": "v5.0.5"}, "symfony/web-profiler-bundle": {"version": "6.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.1", "ref": "e42b3f0177df239add25373083a564e5ead4e13a"}, "files": ["config/packages/web_profiler.yaml", "config/routes/web_profiler.yaml"]}, "symfony/workflow": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "3b2f8ca32a07fcb00f899649053943fa3d8bbfb6"}, "files": ["./config/packages/workflow.yaml"]}, "symfony/yaml": {"version": "v5.0.5"}, "symfonycasts/tailwind-bundle": {"version": "v0.6.0"}, "tales-from-a-dev/flowbite-bundle": {"version": "0.7", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "0.4", "ref": "8c5eef17730535682128557a1016872fd3e81c33"}}, "tales-from-a-dev/twig-tailwind-extra": {"version": "0.2", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "0.2", "ref": "7243ab070ed66198eb82c026684e9b9773e7b64a"}}, "tattali/calendar-bundle": {"version": "1.1", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "1.1", "ref": "0c643e17b5340747b35c2c75e11727fa31bfa9bb"}, "files": ["./config/routes/calendar.yaml"]}, "theseer/tokenizer": {"version": "1.2.0"}, "tijsverkoyen/css-to-inline-styles": {"version": "2.2.2"}, "twig/cssinliner-extra": {"version": "v3.0.3"}, "twig/extra-bundle": {"version": "v3.0.3"}, "twig/inky-extra": {"version": "v3.0.3"}, "twig/intl-extra": {"version": "v3.0.3"}, "twig/string-extra": {"version": "v3.0.5"}, "twig/twig": {"version": "v3.0.3"}, "webmozart/assert": {"version": "1.7.0"}, "yokai/enum-bundle": {"version": "v4.0.3"}, "zenstruck/foundry": {"version": "1.19", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.10", "ref": "37c2f894cc098ab4c08874b80cccc8e2f8de7976"}, "files": ["config/packages/zenstruck_foundry.yaml"]}, "zenstruck/mailer-test": {"version": "v1.4.2"}, "zenstruck/messenger-test": {"version": "1.11", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.7", "ref": "e0d2a904cd584d15bcbb4c4a011549840bc01daf"}}}