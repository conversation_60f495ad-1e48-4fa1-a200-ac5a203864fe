version: '3'

tasks:
    fixtures:
        desc: Generate fixtures for dev environment
        cmds:
            - symfony console d:d:d --force --if-exists --quiet
            - symfony console d:d:c --quiet
            - symfony console d:s:u --force --quiet
            - symfony console d:m:sync-metadata-storage --quiet
            - symfony console d:m:v --add --all --quiet
            - symfony console d:f:l --no-interaction --quiet

    composer:
        desc: Install PHP vendors
        cmds:
            - composer install
        sources:
            - composer.lock
        generates:
            - vendor/**/*

    start:
        desc: Start Docker containers & Symfony server
        cmds:
            - docker compose -p=batcoscom up -d
            - symfony serve -d

    stop:
        desc: Stop Docker containers & Symfony server
        cmds:
            - docker compose -p=batcoscom stop
            - symfony server:stop

    test:
        desc: Run tests
        cmds:
            - task: setup_tests
            - symfony run php bin/phpunit

    coverage:
        desc: Run tests with coverage
        cmds:
            - task: setup_tests
            - symfony php -dpcov.enabled=1 bin/phpunit --coverage-html=public/coverage

    setup_tests:
        cmds:
            - symfony console d:d:d --force --if-exists --quiet --env=test
            - symfony console d:d:c --quiet --env=test
            - symfony console d:s:u --force --quiet --env=test
            - symfony console d:f:l --no-interaction --quiet --env=test

    ci:
        desc: Check code style, static analysis...
        cmds:
            - symfony composer ci

    cs-fix:
        desc: Fix code style
        cmds:
            - symfony composer cs:fix

    node_modules:
        desc: Update frontend vendors
        cmds:
            - yarn install
        sources:
            - yarn.lock
        generates:
            - node_modules/**/*

    assets:
        desc: Build frontend assets
        cmds:
            - task: node_modules
            - yarn run dev
        sources:
            - assets/**/*
        generates:
            - public/build/**/*
